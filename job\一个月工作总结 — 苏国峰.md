# 一个月工作总结 — 苏国峰

## 一、工作内容总结

### 第一周：软路由系统搭建与主机硬件安装

- 学习并选用IStoreOS系统完成软路由安装与配置，搭建稳定离线下载环境；
- 成功部署Aria2，实现离线下载功能，解决了下载路径和openwrt兼容性问题，保障软路由下载稳定性；
- 完成4台主机硬件组装，包括机箱、电源、硬盘、内存、显卡等关键组件；
- 安装操作系统，其中三台为rockylinx9桌面版，一台为Windows专业版。

### 第二周：新增设备配置及异地组网搭建测试

- 新增软路由及磁盘阵列存储，重新配置RAID阵列（RAID0）提升存储性能；
- 完成软路由统一系统配置及挂载统一存储路径，确保下载任务稳定保存数据；
- 使用FeiShuVpn搭建异地VPN组网，并实际在校园网环境测试，初步验证组网可用性（校园网认证偶发问题）；
- 发现组网存在OOM内存泄漏导致掉线现象，后续第三周完成问题定位及解决。

### 第三周：软路由存储管理与数据库运维

- 全面格式化和调整软路由存储的RAID模式，优化存储性能，实现多路软路由同时挂载与下载；
- 编写自动分配脚本，将大量种子文件平均分发至不同软路由节点，提高下载效率和负载均衡；
- 开发组网状态检测脚本，通过监控tun0网卡及IP，自动重启异常服务，确保网络稳定；
- 配合数据分析团队，讨论数据同步需求和改进方案；
- 配置MySQL数据库，开启通用查询及错误日志，方便检查访问及异常操作；
- 编写数据库自动备份脚本，保障数据安全；
- 设计并实现登录行为实时监控及报警，针对登录失败次数和频繁成功登录设置报警阈值，通过钉钉机器人推送相关信息；
- 新增数据库用户并优化权限管理，增强数据库安全；
- 调整报警机制，将钉钉报警改为本地日志记录，方便后续排查；
- 持续关注软路由及组网状态，确认各种脚本及备份任务正常运行，确保系统稳定。

### 第四周：数据库管理优化、系统监控及Odoo部署

- 持续完善数据库备份与定时任务，确保备份新库正常完成；
- 优化MySQL登录日志监控脚本，提高异常登录记录准确性；
- 定期清理临时日志，释放系统存储空间，保证服务器稳定；
- 对通用日志开启轮转功能，防止磁盘爆满
- 替换Linux开源显卡驱动为NVIDIA官方驱动，解决因驱动导致的服务器卡死问题；
- 换官方驱动后还是出现卡死问题，求助木森老师帮助排查
- 部署Netdata监控面板，实现CPU、内存、磁盘、网络资源的实时图形化监控，方便远程故障排查；
- 使用Anaconda虚拟环境部署Odoo 18.0，成功解决依赖问题；
- 启用并调试Odoo项目管理模块，深化对业务流程的理解；
- 集成systemd管理服务，优化Odoo启动与稳定性；
- 完成Odoo部署相关文档编写，为后续快速部署打基础。

### Aria2下载环境完善与文献下载自动化开发

- 持续监控优化Aria2下载服务，调整连接数及种子导入策略；
- 探索将下载内容自动同步到阿里云盘、易有云及Alist网盘的方案；
- 实现通过RPC接口自动添加下载任务、查询状态及日志记录，提升管理效率；
- 编写文献自动下载Python脚本，支持根据文献名称结合学校内网及期刊权限自动解析下载链接；
  - 利用 Python 脚本，支持通过文献名解析下载链接（结合学校内网及期刊权限）；
  - 查阅并测试了多种文献资源获取工具，包括基于搜索引擎解析的 SciHub 工具链、libgen 接口调用等；
  - 配套编写 Shell 脚本，实现批量处理 `.txt` 文献名：
    - 成功文献备份，失败任务记录日志；
    - 自动触发 Aria2 下载任务，避免依赖 Sci-Hub；
    - 增强容错机制，确保失败任务不误删源文件。
  - 确保执行过程中，**失败任务不会误删源文件**，增强错误容忍度与后续排查能力。

### 软路由组网与远程跳板搭建

- 使用易有云实现软路由与其他终端之间的内网穿透与组网连接；
- 易有云组网替代feishuVPN，将feishu vpn舍弃；
- 初步探索分布式多节点远程环境；
- 实现远程拉取文件及下载功能，满足远程协作需求。

### 第五周（进行中）：

- 检查与核实MySQL数据库定时备份任务执行状况与备份文件完整性，清理冗余备份释放存储空间；
- 优化现有Shell与Python文献下载脚本，增强日志记录与错误处理；
- 设计基于Flask框架的轻量级Web服务开发，实现前端文献名称输入提交下载请求；
- 引入代理服务配置，提升跨网络环境下文献请求成功率；
- 完成多环境测试，确保Web页面能够稳定调用自动下载功能，处理网络异常与文献名称错误提示。

#### 部分成果截图

![image-20250630172145007](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250630172145007.png)

![image-20250630172210545](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250630172210545.png)

![image-20250630172239834](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250630172239834.png)

![image-20250630172353327](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250630172353327.png)

![image-20250630173012949](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250630173012949.png)

