# 2025年7月工作月报

## 一、月度工作概述

7月份工作主要分为三个阶段：
- **上旬（7/1-7/10）**：文献下载平台功能优化与系统维护
- **中旬（7/11-7/20）**：Odoo系统学习与软路由网络部署
- **下旬（7/21-7/31）**：大规模数据处理与容器化迁移准备

---

## 二、按时间顺序的主要工作内容

### 2.1 第一周（7/1-7/7）：文献下载平台优化与网络基础建设

#### 7/1 文献下载系统代理功能开发
- 为文献下载Web系统引入代理功能
- 修复直连代理方式下访问异常的问题
- 增加代理失效时的异常处理逻辑
- 前往学校进行现场测试，验证校园网下代理服务连通性
- 编写Web服务开机自启脚本

#### 7/2 软路由部署与异地互联方案设计
- 完成软路由设备配置脚本整理和Web服务部署
- 基于"易有云"工具设计两套异地互联解决方案：
  - 方案一：基于Windows中转访问
  - 方案二：软路由端口映射+易有云远程转发
- 完成方案文档撰写与验证测试

#### 7/3-7/7 系统维护与故障排查
- 持续进行数据库备份检查和日志归档
- 排查Linux机器卡死问题，重点分析网卡驱动与中断负载

### 2.2 第二周（7/8-7/14）：平台功能增强与系统稳定性提升

#### 7/8-7/9 Linux系统故障深度排查
- 深入排查Linux机器卡死问题
- 调整网卡中断分配，优化系统参数
- 进行IRQ分配与内核参数调整测试

#### 7/10 文献下载平台重大功能升级
- **PDF解析能力增强**：大幅提升解析准确性和成功率
- **Unpaywall API集成**：完成API集成并增强页面爬取兜底机制
- **Sci-Hub镜像集成**：集成20多个最新Sci-Hub镜像作为兜底方案
- **DOI格式兼容性优化**：支持多种DOI格式输入（标准DOI、带前缀DOI、期刊官网URL等）
- 支持查找a、link、button、iframe、embed等标签中的PDF下载入口

### 2.3 第三周（7/15-7/21）：Odoo系统学习与邮件服务配置

#### 7/15 软路由网络管理与Odoo系统学习启动
- 将所有软路由设备集中管理，通过交换机连接网络
- 开始深入学习Odoo系统各模块：员工管理、发票管理、项目管理、CRM、采购管理

#### 7/16 Odoo邮件服务与软路由恢复
- 配置Odoo邮件发送服务器，测试163邮箱SMTP功能
- 排查邮件发送失败原因（发件人验证失败等）
- 处理软路由断电后的下载任务恢复，验证Aria2断点续传功能

#### 7/17-7/21 Odoo系统深度学习
- 继续学习Odoo各模块功能与管理流程
- 理清各模块间的联动关系
- 为后续实际使用和培训打下基础

### 2.4 第四周（7/22-7/28）：大规模数据处理与容器技术研究

#### 7/22 种子下载监控与文件传输优化
- 完成种子下载持续统计和监控
- 对潘老师提供的6000多条DOI进行下载进度观察
- 总结多种文件传输方法：解压后传输、SCP命令传输、直接下载
- 增加PDF文件数量检查和打包下载功能
- 测试Podman容器技术，发现其与Docker的兼容性

#### 7/23-7/24 数据处理与系统优化
- 继续DOI下载任务的进度跟踪
- 优化文件管理和传输流程

#### 7/25 DOI下载任务完成与Odoo模块开发
- **重要里程碑**：完成六千多条DOI文献下载任务
- 将所有下载文件整理导出至本地并同步至移动存储设备
- 解决Odoo模块中报表报错及数据库切换功能缺失问题
- 实现项目模块完整功能，包括自定义模板字段展示
- 梳理PostgreSQL与MySQL数据库间的迁移策略

### 2.5 第五周（7/29-7/31）：容器化迁移与系统培训

#### 7/29 容器化迁移测试
- 对Odoo服务、PostgreSQL和MySQL数据库进行完整模拟迁移测试
- 使用Docker成功还原并部署所有服务
- 手动构建Odoo镜像并完成本地打包
- 完成监控系统镜像准备：Prometheus、Grafana、Node Exporter、cAdvisor
- 整理迁移流程与操作文档

#### 7/30 监控系统准备与进度整理
- 继续完善容器化部署方案
- 整理软路由种子下载进度数据

#### 7/31 Odoo系统培训与备份脚本优化
- **用户培训**：对三位行政同事进行Odoo系统基础使用培训
- 介绍Odoo模块化架构、用户管理流程、权限组配置
- 将MySQL自动备份脚本适配为容器环境使用
- 完成监控系统docker-compose.yml编写

---

## 三、重要成果与亮点

### 3.1 技术成果
1. **文献下载平台**：成功下载6000+DOI文献，系统稳定性和成功率显著提升
2. **容器化迁移**：完成核心业务系统的容器化迁移测试，为正式迁移做好准备
3. **监控体系**：建立完整的Prometheus + Grafana监控方案
4. **网络架构**：设计并实现异地互联解决方案，提升网络连通性

### 3.2 系统优化
1. **备份策略**：实现数据库备份从物理机到容器环境的平滑迁移
2. **自动化程度**：通过脚本优化提升系统自动化水平
3. **用户体验**：Odoo系统培训为后续正式上线降低适应成本

---

## 四、遇到的问题与解决方案

### 4.1 技术问题
- **Linux系统卡死**：通过网卡驱动与中断负载分析，逐步排查并解决
- **邮件服务配置**：解决SMTP验证限制问题，确保发件人地址与认证用户一致
- **软路由断电恢复**：验证Aria2断点续传功能，增强系统稳定性

### 4.2 性能优化
- **文件传输**：总结多种文件传输方法，优化大文件传输效率
- **下载速度**：发现迅雷在Windows系统下载速度优势，但传输到软路由存储时需优化

---

## 五、下月工作计划

### 5.1 系统部署
- 在新环境中正式部署Odoo、数据库服务与资源监控组件
- 测试容器资源限制配置对实际服务运行的影响
- 搭建完整的Prometheus + Grafana监控体系

### 5.2 功能完善
- 配合行政使用反馈调整Odoo权限和操作逻辑
- 持续优化镜像构建与部署流程，提升迁移效率与稳定性
- 整合软路由下载进度数据到可视化系统中

### 5.3 技术提升
- 深入研究容器编排技术，提升系统管理效率
- 完善监控告警机制，确保系统稳定运行
- 继续优化文献下载平台的性能和用户体验

---

## 六、总结

7月份工作重点围绕系统现代化改造和基础设施优化展开，在文献下载平台、Odoo系统部署、容器化迁移等方面取得了显著进展。通过系统性的学习和实践，为后续的正式迁移和系统升级奠定了坚实基础。下月将重点推进正式部署和监控体系建设，确保系统稳定高效运行。
