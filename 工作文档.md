# 工作文档

#### 目前工作

- 整理种子下载进度，对应两个表格，表明种子文件所在位置

- 软路由序号与对应ip

  | istoreos-1(位置在学校，目前无法访问) | istoreos-2   | istoreos-3   | istoreos-4     | istoreos-5     |
  | ------------------------------------ | ------------ | ------------ | -------------- | -------------- |
  |                                      | ************ | ************ | ************** | ************** |

**均可以在局域网可访问直接访问，查看下载进度位置如下**

![image-20250910143013408](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250910143013408.png)

- 表格进度为 [软路由下载进度.xlsx](软路由下载进度.xlsx) 

#### odoo平台管理

- odoo的部署方式为docker-compose部署，并且利用frp公网穿透穿透到公网************，并且还加了一层nginx
- 部署维护备份文档为 [odoo与postgre迁移——docker-compose版.pdf](odoo与postgre迁移——docker-compose版.pdf) 

**包含odoo如何迁移，如何部署，如何备份，如何维护**

- odoo二次开发目录所在位置为/opt/odoo/odoo-server/addons/hongzhiwei_project/

- 如果需要再次修改源码，修改后可以重启odoo服务

  ```
  docker restart odoo-server
  ```

- 由于挂载了持久化目录，在宿主机修改文件，容器内会跟着变化，只需重启即可

- 备份脚本所在位置/opt/odoo/backup，备份目录为/opt/odoo/odoo-db-backup/

- odoo所需数据库为postgresql,也是通过docker-compose部署，可以查看docker-compose文件。

**所有服务均部署在/opt目录，对应的服务下面有docker-compose.yaml文件**

#### eam固定资产平台管理

- eam的部署方式也是为docker-compose方式部署，并且利用frep公网穿透到公网************/eam/,也加了一层nginx
- 部署维护备份文档为 [eamapp部署.pdf](eamapp部署.pdf) 

**所需镜像所在仓库如下**

```
registry.cn-hangzhou.aliyuncs.com/eamapp/minio:20240803
registry.cn-hangzhou.aliyuncs.com/eamapp/nginx:fapp_nginx_2.9.1.5
registry.cn-hangzhou.aliyuncs.com/eamapp/app:fapp_bpm_2.9.1.5
registry.cn-hangzhou.aliyuncs.com/eamapp/app:fapp_app_2.9.1.5
registry.cn-hangzhou.aliyuncs.com/eamapp/mysql:fapp_db_2.9.1.5
```

**详情也可以文件与docker-compose文件**

- eam是由java开发，所以如果要进行二次开发需要重新构建jar包，私库问题在文档中已经解决
- 备份脚本所在位置为/opt/eamapp/backup/，备份目录为/opt/eamapp/db-backup/

#### nginx代理

- Nginx代理也是由docker-compose部署，主要代理odoo与eamapp，部署包含在两个文档中

- 需要注意的就是nginx的配置文件conf.d/

#### Mysql数据库

- 由原来的数据工程师所用的数据库，一并迁移，也是用docker-compose方式部署

- 端口为3306,可以直接用公网访问数据库

- 账号分配如下

  ```
  root@localhost         Admin@12345
  root@%                 A8d!eP@#9xLq2025
  testuser               NewBh@888888
  analyst                StrongP@ssw0ed!
  ```

- 暂时未进行备份

#### 云冈石窟监测平台

- 配合其他同事进行监测的一个平台，需要观察是否正常监测即可，每两小时进行监测
- 数据输出目录为E:/云冈石窟

#### 东方超算实例维护

- 按端口号使用人进行启动实例，遇到问题及时向客服反馈
- 使用人对应表格如下 [超算实例.xlsx](超算实例.xlsx) 

#### 鸿之微服务器

- 维护登录节点与计算节点的问题，例如无法连接，无法ssh等，解决处理。

#### **工作站管理**

- 所有服务均部署在工作站上面工作站的ip与密码如下

  ```
  *************
  root  12345678
  ```

- 并且部署了监控，监控宿主机资源与容器资源使用情况分别如下

  - *************:3000
  - 账号admin
  - 密码：admin@123

  