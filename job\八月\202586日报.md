### 2025/8/6日报

#### 今日工作内容：

1. **GPU 容器技术研究与验证**
   - 研究了容器（Docker）中使用 GPU 的关键技术方案，包括 NVIDIA Container Toolkit 的配置与容器运行参数设置（如 `--gpus` 参数）。
   - 测试了在容器中执行深度学习任务时 GPU 的实际负载情况，结合 `nvidia-smi` 实时监控，验证容器内任务对 GPU 的调用无性能损耗，与物理环境运行对比一致。
   - 探索了容器内多 GPU 分配的可行方案（如指定 `device=0,1,2` 等），为后续多任务调度和资源隔离提供技术准备。
2. **GPU 运维环境优化工作准备**
   - 整理从裸机到容器使用 GPU 的全流程，包括驱动安装、CUDA/CuDNN环境搭建、Python3.10+ 编译安装、PyTorch 环境部署，为形成标准化部署文档做前期准备。
   - 初步规划后续可实战演练方向，如推理服务部署、GPU 多容器并发测试、AI 模型导出与部署流程等。