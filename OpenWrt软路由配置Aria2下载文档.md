### OpenWrt软路由配置Aria2下载文档

一、基础操作

​	连通外网，配置openwrt连通外网具体操作如下

```
#ip a 与ifconfig观察，pve中外网卡与vmbr2绑定，只需配置vmbr2设置dhcp或者静态ip
#配置路径 /etc/network/interfaces
auto vmbr2
iface vmbr2 inet static
        address **************/24
        gateway ************
        bridge-ports enp2s0
        bridge-stp off
        bridge-fd 0
```

外网连通后进入pve的web界面打开对应openwrt虚拟机

配置openwrt连通外网，设置静态ip

```
config device
        option name 'eth1'

config interface 'wan'
        option proto 'static'
        option device 'eth1'
        option ipaddr '************01'
        option netmask '*************'
        option gateway '**************'
        option dns '******* *******'
```

pve中连通openwrt网络

```
qm set 100 --net1 virtio,bridge=vmbr2,firewall=0
```

二、openwrt安装相关依赖与环境

```
opkg update
opkg install luci-app-aria2 luci-i18n-aria2-zh-cn
opkg install aria2
```

编写aria2的配置文件启动并设置开机自启

```
root@OpenWrt:/tmp/tmp# cat /etc/aria2.conf 
dir=/mnt/sdc/downloads
enable-rpc=true
rpc-listen-all=true
rpc-listen-prot=6800
rpc-allow-origin-all=true
log=/var/log/aria2.log
max-concurrent-downloads=5
continue=true
```

启动

```
aria2c --conf-path=/etc/aria2.conf -D
```

挂载usb存储

在pve的web页面关联硬盘

```
#由于缺少命令，通过日志查看usb存储id
dmesg | grep usb
```

挂载

```
mount /dev/sdc /mnt/sdc
```

创建下载目录

```
mkdir  -p /mnt/sdc/downloads
chmod 777 /mnt/sdc/downloads
```

三、上传AriaNG

```
通过winscp上传，解压并移动到/www/目录下
```

测试

访问web页面

```
http//路由ip/ariang
```

