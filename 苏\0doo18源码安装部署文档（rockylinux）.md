### ndoo18源码安装部署文档（rockylinux）

#### 一、系统环境准备

```
# 1. 更新系统
sudo dnf update -y

# 2. 安装依赖
sudo dnf install -y gcc git python3-devel libxslt-devel bzip2-devel \
  openldap-devel libjpeg-devel freetype-devel postgresql-server \
  postgresql-devel libpq-devel xz-devel libffi-devel redhat-rpm-config \
  libxml2-devel libXrender libXext libX11

# 3. 安装 EPEL
sudo dnf install epel-release -y

# 4. 安装 Node.js（建议使用 16）
curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
sudo dnf install -y nodejs --nogpgcheck

# 5. 安装 Less 和 CleanCSS
sudo npm install -g less less-plugin-clean-css
```

#### 二、安装wkhtmltopdf（PDF渲染支持）

```
cd /opt
wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.centos8.x86_64.rpm
sudo dnf install -y xorg-x11-fonts-Type1
sudo dnf install -y ./wkhtmltox-0.12.6-1.centos8.x86_64.rpm
```

#### 三、安装并初始化PostgreSql

```
sudo postgresql-setup --initdb
sudo systemctl enable --now postgresql
#切换postgres用户
sudo -i -u postgres
psql
# 创建 odoo 数据库用户
CREATE USER odoo WITH PASSWORD 'admin@123';
ALTER USER odoo WITH CREATEDB;
\q
exit
#修改postgres密码
sudo -u postgres
psql
ALTER USER postgres WITH PASSWORD 'Admin@12345';
\q
exit
#编辑 pg_hba.conf 文件
vi /var/lib/pgsql/data/pg_hba.conf
local   all             all                                     peer
改为
local   all             all                                     md5

```

#### 3.1备用安装PostgreSQL15（如果版本小于12的话安装）

```
1.查看版本
psql -U postgres -c "SELECT version();"
2.卸载旧版本
sudo systemctl stop postgresql
sudo dnf remove postgresql* -y
3.安装PostrgeSQL
# 添加阿里云 PostgreSQL 15 的 YUM 源
rpm -Uvh https://mirrors.aliyun.com/postgresql/repos/yum/reporpms/EL-7-x86_64/pgdg-redhat-repo-latest.noarch.rpm
sed -i "s@https://download.postgresql.org/pub@https://mirrors.aliyun.com/postgresql@g" /etc/yum.repos.d/pgdg-redhat-all.repo
dnf clean all
dnf makecache
# 禁用系统默认 PostgreSQL 模块
sudo dnf -qy module disable postgresql
# 安装 PostgreSQL 15 及扩展
dnf install -y postgresql15 postgresql15-server postgresql15-contrib
4.初始化并启动PostgreSQL 15
sudo /usr/pgsql-15/bin/postgresql-15-setup initdb
sudo systemctl enable --now postgresql-15
```

#### 四、安装python3.11

```
rockylinux默认没有，需要手动编译
# 安装编译依赖
sudo dnf groupinstall "Development Tools" -y
sudo dnf install -y gcc openssl-devel libffi-devel zlib-devel bzip2-devel

# 下载源码编译安装 Python 3.11
cd /usr/src
sudo curl -O https://www.python.org/ftp/python/3.11.9/Python-3.11.9.tgz
sudo tar xvf Python-3.11.9.tgz
cd Python-3.11.9
sudo ./configure --enable-optimizations
sudo make -j$(nproc)
sudo make altinstall

# 确认安装
python3.11 --version
```

**安装Anaconda虚拟环境**

```
cd /opt
wget https://repo.anaconda.com/archive/Anaconda3-2023.07-1-Linux-x86_64.sh
bash Anaconda3-2023.07-1-Linux-x86_64.sh
# 安装过程按提示执行（建议安装到 /opt/anaconda3）

# 激活 Anaconda 环境
source ~/.bashrc

# 创建并激活虚拟环境
conda create -n odoo18 python=3.11 -y
conda activate odoo18
```

#### 五、创建Odoo用户与目录结构

```
sudo useradd -m -U -r -d /opt/odoo -s /bin/bash odoo
sudo mkdir -p /opt/odoo/odoo-server
sudo chown -R odoo: /opt/odoo
```

六、下载Odoo源码并安装依赖

```
# 切换到 odoo 用户
sudo su - odoo

# 上传源码包
 tar -xzf odoo-18.0.tar.gz -C /opt/odoo/odoo-server --strip-components=1
cd odoo-server


指定使用python3.11安装依赖
/usr/local/bin/python3.11 -m ensurepip
/usr/local/bin/python3.11 -m pip install --upgrade pip
/usr/local/bin/python3.11 -m pip install -r /opt/odoo/odoo-server/requirements.txt
```

#### 七、配置Odoo

```
mkdir /opt/odoo/etc /opt/odoo/log
vi /opt/odoo/etc/odoo.conf
```

```
[options]
admin_passwd = admin
db_host = localhost
db_port = 5432
db_user = odoo
db_password = admin@123
addons_path = /opt/odoo/odoo-server/addons
logfile = /opt/odoo/log/odoo.log
xmlrpc_port = 8069
xmlrpc_interface = 0.0.0.0
```

#### 八、创建systemd服务文件

```
sudo nano /etc/systemd/system/odoo.service
```

```
[Unit]
Description=Odoo 18
After=network.target postgresql.service

[Service]
ExecStart=/usr/local/bin/python3.11 /opt/odoo/odoo-server/odoo-bin -c /opt/odoo/etc/odoo.conf
Restart=always
User=root
Group=root

[Install]
WantedBy=multi-user.target

```

#### 九、启动Odoo

```
sudo systemctl daemon-reexec
sudo systemctl daemon-reload
sudo systemctl enable --now odoo
```

#### 十、访问Odoo

```
http://<你的服务器IP>:8069
```

防火墙放行

```
sudo firewall-cmd --permanent --add-port=8069/tcp
sudo firewall-cmd --reload
```

#### 十一、验证端口监听状态

```
ss -tuln | grep 8069

```

#### 十二、退出虚拟环境

```
conda deactivate
```

#### 十三、nginx反向代理（可选）

```
安装nginx
sudo dnf install nginx -y
sudo systemctl enable --now nginx
```

**配置nginx文件**

```
#vim /etc/nginx/conf.d/odoo.conf
server {
    listen 8069;
    server_name erp.odoo.com odoo.erp.com erp.odoo.com.cn *************;

    return  301 https://$host$request_uri;
}
server {
    listen 443 ssl;
    server_name erp.odoo.com odoo.erp.com *************;  # 多个域名和 IP

    ssl_certificate /etc/nginx/ssl/certs/server.crt;
    ssl_certificate_key /etc/nginx/ssl/private/server.key;
    location / {
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

```

