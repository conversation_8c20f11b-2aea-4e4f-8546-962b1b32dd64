### 2025/8/11日报

### 今日工作内容

1. **种子下载任务整理与调度**
   - 对学校的种子下载任务**停止所有正在执行的下载任务**，原因下载速度很慢。
   - 将任务重新分配至**本地三台软路由**，实现下载任务的**均衡分配**，提高整体下载效率。
   - 对其他来源已完成或正在下载的对应任务进行**清理删除**，避免重复下载。
   - 将全部任务整理至**统一表格**，并**使用颜色标记下载进度和状态**，确保后续下载与管理的直观性与准确性。
2. **容器技术文档编写与完善**
   - 持续编写《容器技术》相关文档，今日新增/完善以下内容：
     - **容器进程管理机制**：容器与宿主机进程关系、PID Namespace 及进程隔离。
     - **OOM（Out Of Memory）机制**：容器内存限制配置、OOM Kill 原理及监控方法。
     - **容器磁盘配额**：overlay2 文件系统空间管理、`--storage-opt` 配置方法与监控。
     - **Docker 基础与进阶**：镜像构建、容器生命周期管理、网络与存储卷。
     - **docker-compose**：多容器编排、服务依赖与环境变量配置。
     - **容器安全**：镜像签名（Image Signing）、SBOM（软件物料清单）、OPA 策略控制等安全治理方法。

