### MySQL 8.0 跳过权限模式重置 root 密码

------

#### 1. 停止正在运行的 MySQL 服务

```
sudo systemctl stop mysqld
```

------

#### 2. 以 mysql 用户身份启动 mysqld，跳过权限验证（不加载权限表）

```
sudo -u mysql /usr/sbin/mysqld --skip-grant-tables --skip-networking &
```

等待几秒，确认 mysqld 启动完成（看日志或等候几秒即可）

------

#### 3. 另开一个终端，登录 MySQL（无密码登录）

```
mysql -uroot
```

------

#### 4. 切换到 mysql 数据库

```
USE mysql;
```

------

#### 5. 清空 root 用户密码（authentication_string 置空）

```
UPDATE user SET authentication_string='' WHERE user='root' AND host='localhost';
FLUSH PRIVILEGES;
```

------

#### 6. 退出 MySQL 客户端

```
exit;
```

------

#### 7. 停止跳过权限的 mysqld 进程

```
sudo killall mysqld
```

------

#### 8. 启动正常的 MySQL 服务

```
sudo systemctl start mysqld
```

------

#### 9. 使用新密码登录（此时密码为空）

```
mysql -uroot
```

------

#### 10. 在 MySQL 里设置 root 用户密码并调整认证插件

```
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'Admin@12345';
FLUSH PRIVILEGES;
```

------

#### 11. 退出 MySQL，使用新密码测试登录

```
mysql -uroot -pAdmin@12345
```