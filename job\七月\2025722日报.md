### 2025/7/22日报

**工作内容**：

1. **种子下载持续统计**：
   - 完成了对种子的持续下载监控，确保了下载任务的稳定性和进度。
   - 在下载过程中，观察到使用**迅雷**（Thunder）在**Windows**系统上下载速度较快，但将下载文件传输到**软路由**存储时速度较慢。这可能与网络条件或软路由的I/O性能有关。
2. **DOI下载进度监控**：
   - 对**潘老师**提供的6,000多条**DOI**进行下载进度的观察和记录，确保所有条目都在顺利下载。
3. **文件传输方法总结**：
   - 探索并总结了以下几种文件夹导出到本地的方法：
     - **解压后传输到本地**：优先采用此方式，能确保文件完整传输并便于后续处理。
     - **使用SCP命令直接传输**：适用于快速传输，但需要考虑网络稳定性。
     - **直接下载**：适用于小文件，效率较高，但在大文件传输时可能存在限制。
     - 对文件夹访问目录增加了pdf文件数量检查，增加了总大小展示，增加了打包下载所有PDF的功能，
4. **容器技术（Podman）命令测试**：
   - 对**Podman**进行了命令测试，发现其命令与**Docker**大致相同，唯一的区别是**Podman**不需要守护进程，这为系统资源的优化提供了便利。

**待办事项**：

- 继续跟踪DOI下载进度，确保高效完成任务。

