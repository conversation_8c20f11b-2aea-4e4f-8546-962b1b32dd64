#### frp+公网穿透代理方案

**整体方案架构图**

```
        🔻 访问设备
         |
         | 访问公网 IP（或域名）+ 端口/域名
         |
     ╔════════════════════╗
     ║ 公网服务器（CentOS）║ ←───── 反向连接 ─────╮
     ╚════════════════════╝                      │
         ↑       frps 监听端口（如7000）         │
         │                                       │
         ↓       映射端口或域名访问              │
     ╔═══════════════════╗                      ↓
     ║ iStoreOS软路由（多台）║ ←───── frpc 客户端配置
     ╚═══════════════════╝

```

frp下载地址https://github.com/fatedier/frp/releases/download/v0.63.0/frp_0.63.0_linux_amd64.tar.gz

##### 公网服务器配置frps.toml

```
bind_port = 7000  # frp 主通讯端口
dashboard_port = 7500  # frp 管理面板
dashboard_user = "admin"
dashboard_pwd = "admin123"
vhost_http_port = 8080  # HTTP 虚拟主机端口
vhost_https_port = 8443  # HTTPS 虚拟主机端口
```

##### 软路由配置frpc.toml

```
serverAddr = "公网"
serverPort = 7000

[[proxies]]
name = "route1_web"
type = "tcp"
localIP = "127.0.0.1"
localPort = 80
remotePort = 8081

[[proxies]]
name = "route1_7788"
type = "tcp"
localIP = "127.0.0.1"
localPort = 7788
remotePort = 8778

[[proxies]]
name = "route1_nas"  
type = "tcp"
localIP = "127.0.0.1"
localPort = 8897
remotePort = 8991

```

##### 配置systemd服务并开机自启

**服务端配置**/etc/systemd/system/frps.service

```
[Unit]
Description=FRP Server Service
After=network.target

[Service]
Type=simple
ExecStart=/opt/frp/frps -c /opt/frp/frps.toml
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

**客户端配置**配置开机启动脚本

```
vi /etc/rc.local
/opt/frp/frpc -c /opt/frp/frpc.toml >/dev/null 2>&1 &
```

**端口分配方案**

```
软路由	内网端口 80	内网端口 7788	内网端口 8897	公网端口（80）	公网端口（7788）	公网端口（8897）
路由1	80	7788	8897	8001	8002	8003
路由2	80	7788	8897	8011	8012	8013
路由3	80	7788	8897	8021	8022	8023
路由4	80	7788	8897	8031	8032	8033
路由5	80	7788	8897	8041	8042	8043

访问软路由1的 Web 界面：http://公网IP:8001

访问软路由1的 7788 端口：http://公网IP:8002

访问软路由1的 NAS：http://公网IP:8003
```

