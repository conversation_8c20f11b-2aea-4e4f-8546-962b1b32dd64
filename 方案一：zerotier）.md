# **方案一：ZeroTier 组网**
---

### **一、ZeroTier 基础组网配置**
#### **1. 注册 ZeroTier 并创建网络**
1. 访问 [ZeroTier 官网](https://my.zerotier.com)，注册账号并登录。
2. 进入 **Networks** 页面，点击 **Create a Network**，生成一个 Network ID（如 `abcdef1234567890`）。

#### **2. 在三台软路由上安装 ZeroTier**
在每台 iStoreOS 上执行：
```bash
opkg update
opkg install zerotier
zerotier-one -d  # 启动 ZeroTier 守护进程
zerotier-cli join abcdef1234567890  # 替换为你的 Network ID
```
- 每台设备会输出类似 `200 join OK` 的提示。

#### **3. 在 ZeroTier 后台授权设备**
1. 返回 ZeroTier 控制台，找到你的 Network，在 **Members** 列表中出现新设备（需等待约 30 秒）。
2. 勾选每台设备前的复选框，为其分配内网 IP（如 `*************`、`*************`、`*************`）。

#### **4. 验证网络连通性**
在任意一台软路由上测试：
```bash
ping *************  # 替换为其他节点的 ZeroTier IP
```
- 若通，表示组网成功。

---

### **二、配置 Aria2 远程访问**
#### **1. 修改 Aria2 配置文件**
在每台软路由上编辑 `/etc/aria2.conf`：
```conf
rpc-listen-all=true
rpc-secret=你的密码  # 设置 RPC 访问密钥
```
重启 Aria2：
```bash
/etc/init.d/aria2 restart
```

#### **2. 通过 ZeroTier 访问 Aria2**
- **Aria2 RPC 接口**：  
  `http://192.168.192.x:6800/jsonrpc`（替换为目标设备的 ZeroTier IP）
- **Aria2 WebUI（如 AriaNg）**：  
  若部署在 8080 端口，访问 `http://192.168.192.x:8080`。

---

### **三、优化 ZeroTier 网络（可选）**
#### **1. 自建 Moon 服务器（提升 NAT 穿透能力）**
若设备间直连不稳定，可在有公网 IP 的服务器上部署 Moon 节点：
```bash
# 安装 ZeroTier
curl -s https://install.zerotier.com | sudo bash
zerotier-one -d

# 生成 Moon 配置
zerotier-idtool initmoon /var/lib/zerotier-one/identity.public > moon.json
# 编辑 moon.json，设置 "stableEndpoints" 为公网 IP（如 "*******/9993"）
zerotier-idtool genmoon moon.json

# 将生成的 .moon 文件复制到所有设备
mkdir -p /var/lib/zerotier-one/moons.d
cp *.moon /var/lib/zerotier-one/moons.d/
```
重启 ZeroTier 生效：
```bash
systemctl restart zerotier-one
```

#### **2. 启用子网路由（访问软路由背后的局域网）**
在软路由上执行：
```bash
zerotier-cli set abcdef1234567890 allowGlobal=true  # 允许全局路由
zerotier-cli set abcdef1234567890 allowDefault=1  # 允许默认路由
```
在 ZeroTier 控制台，勾选设备对应的 **Allow Ethernet Bridging** 选项。

---

### **四、三台软路由的统一管理**
#### **场景 1：负载均衡 Aria2 任务**
通过脚本轮询三台设备的 Aria2 RPC：
```bash
#!/bin/bash
SERVERS=("*************" "*************" "*************")
for server in "${SERVERS[@]}"; do
  curl -X POST "http://${server}:6800/jsonrpc" -d '{
    "jsonrpc": "2.0",
    "id": "1",
    "method": "aria2.addUri",
    "params": ["token:你的密码", ["https://example.com/file.iso"]]
  }'
done
```

#### **场景 2：统一 Web 管理界面**
在一台软路由上部署 **AriaNg**：
```bash
opkg install nginx
cd /www
wget https://github.com/mayswind/AriaNg/releases/download/1.3.6/AriaNg-1.3.6.zip
unzip AriaNg-1.3.6.zip
```
通过 ZeroTier IP 访问：`http://192.168.192.x/AriaNg`。

---

### **五、故障排查**
1. **设备未显示在 ZeroTier 控制台**  
   - 检查 `zerotier-one -d` 是否运行。
   - 确认 Network ID 输入正确。

2. **Ping 不通其他节点**  
   - 在 ZeroTier 控制台检查设备是否已授权。
   - 尝试重启 ZeroTier：`systemctl restart zerotier-one`。

3. **Aria2 RPC 无法访问**  
   - 检查防火墙规则：  
     ```bash
     uci add firewall rule
     uci set firewall.@rule[-1].name='Allow-Aria2-RPC'
     uci set firewall.@rule[-1].src='*'
     uci set firewall.@rule[-1].target='ACCEPT'
     uci set firewall.@rule[-1].proto='tcp'
     uci set firewall.@rule[-1].dest_port='6800'
     uci commit
     /etc/init.d/firewall restart
     ```


```
zerotier-cli status       # 查看状态
zerotier-cli listnetworks # 查看已加入的网络
zerotier-cli join xxxxxx  # 加入网络（如果还没加入）
退出指定网络
zerotier-cli leave <nwid>  # 替换为你的 Network ID
```

```
彻底卸载zerotier
# 停止服务
killall zerotier-one

# 删除 ZeroTier 配置文件（谨慎操作！）
rm -rf /var/lib/zerotier-one

# 卸载软件（iStoreOS/OpenWRT）
opkg remove zerotier
```

### *诊断命令汇总**

1. Windows 端：

   ```
   zerotier-cli status      # 检查服务状态
   zerotier-cli listpeers   # 查看是否连上 Moon/根服务器
   ping ***********         # 测试软路由 IP
   ```

2. 软路由端：

   ```
   ifconfig zt0             # 检查 ZeroTier 接口
   iptables -L -v -n        # 查看防火墙规则
   ```

## **4. 远程访问下载的文件**

### **方法 1：Samba/NFS 共享**

- **在软路由上启用 Samba**：

  ```
  opkg install samba4-server
  uci set samba.@samba[0].enabled='1'
  uci set samba.@samba[0].name='OpenWrt'
  uci set samba.@samba[0].workgroup='WORKGROUP'
  uci set samba.@samba[0].homes='1'
  uci commit
  /etc/init.d/samba start
  ```

- **在远程设备访问**：

  - Windows：`\\<Zerotier IP>\downloads`
  - macOS/Linux：`smb://<Zerotier IP>/downloads`

### **方法 2：Web 文件管理（如 FileBrowser）**

- **安装 FileBrowser**：

  ```
  opkg install filebrowser
  filebrowser -a 0.0.0.0 -p 8080 -r /mnt/sda1/downloads
  ```

- **访问**：

  ```
  http://<Zerotier IP>:8080
  ```

### **方法 3：SFTP/SSH 访问**

- 使用 WinSCP（Windows）或 Cyberduck（macOS）连接：

  ```
  SFTP 地址：<Zerotier IP>
  用户名：root
  密码：<你的路由器密码>
  ```