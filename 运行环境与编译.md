## **一、C/C++**
### **1. 安装编译器**
- **Ubuntu/Debian**:
  ```bash
  sudo apt update
  sudo apt install build-essential  # 包含gcc, g++, make等
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum groupinstall "Development Tools"
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S base-devel
  ```

### **2. 编译方式**
```bash
gcc hello.c -o hello  # C
g++ hello.cpp -o hello  # C++
./hello  # 运行
```

---

## **二、Python**
### **1. 安装Python**
- **Ubuntu/Debian**:
  ```bash
  sudo apt install python3 python3-pip
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum install python3 python3-pip
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S python python-pip
  ```

### **2. 运行方式**
```bash
python3 script.py  # 直接运行
pip install package  # 安装依赖
```

---

## **三、Java (OpenJDK)**
### **1. 安装JDK**
- **Ubuntu/Debian**:
  ```bash
  sudo apt install openjdk-17-jdk  # 选择版本（11/17等）
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum install java-17-openjdk-devel
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S jdk-openjdk
  ```

### **2. 编译与运行**
```bash
javac Hello.java  # 编译
java Hello        # 运行
```

---

## **四、Go**
### **1. 安装Go**
- **Ubuntu/Debian**:
  ```bash
  sudo apt install golang
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum install golang
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S go
  ```

### **2. 编译与运行**
```bash
go build hello.go  # 编译
./hello            # 运行
go run hello.go    # 直接运行（不生成二进制）
```

---

## **五、Rust**
### **1. 安装Rust**
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source $HOME/.cargo/env
```

### **2. 编译与运行**
```bash
rustc hello.rs -o hello  # 编译
./hello                 # 运行
cargo new project       # 创建项目（推荐使用Cargo）
cargo build             # 构建
cargo run               # 运行
```

---

## **六、Node.js (JavaScript)**
### **1. 安装Node.js**
- **Ubuntu/Debian**:
  ```bash
  curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
  sudo apt install nodejs
  ```
- **CentOS/RHEL**:
  ```bash
  curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
  sudo yum install nodejs
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S nodejs npm
  ```

### **2. 运行方式**
```bash
node app.js          # 直接运行
npm install package  # 安装依赖
```

---

## **七、Ruby**
### **1. 安装Ruby**
- **Ubuntu/Debian**:
  ```bash
  sudo apt install ruby-full
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum install ruby
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S ruby
  ```

### **2. 运行方式**
```bash
ruby script.rb       # 直接运行
gem install rails    # 安装依赖
```

---

## **八、PHP**
### **1. 安装PHP**
- **Ubuntu/Debian**:
  ```bash
  sudo apt install php
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum install php
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S php
  ```

### **2. 运行方式**
```bash
php script.php       # 直接运行
```

---

## **九、.NET (C#)**
### **1. 安装.NET SDK**
- **Ubuntu/Debian**:
  ```bash
  wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
  sudo dpkg -i packages-microsoft-prod.deb
  sudo apt update
  sudo apt install dotnet-sdk-6.0
  ```
- **CentOS/RHEL**:
  ```bash
  sudo rpm -Uvh https://packages.microsoft.com/config/centos/7/packages-microsoft-prod.rpm
  sudo yum install dotnet-sdk-6.0
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S dotnet-sdk
  ```

### **2. 编译与运行**
```bash
dotnet new console -o MyApp  # 创建项目
dotnet run                  # 运行
```

---

## **十、Haskell (GHC)**
### **1. 安装GHC**
- **Ubuntu/Debian**:
  ```bash
  sudo apt install ghc
  ```
- **CentOS/RHEL**:
  ```bash
  sudo yum install ghc
  ```
- **Arch Linux**:
  ```bash
  sudo pacman -S ghc
  ```

### **2. 编译与运行**
```bash
ghc hello.hs -o hello  # 编译
./hello                # 运行
```

---

## **总结**
| 语言    | 安装方式（Ubuntu）            | 编译/运行方式          |
| ------- | ----------------------------- | ---------------------- |
| C/C++   | `apt install build-essential` | `gcc hello.c -o hello` |
| Python  | `apt install python3`         | `python3 script.py`    |
| Java    | `apt install openjdk-17-jdk`  | `javac Hello.java`     |
| Go      | `apt install golang`          | `go build hello.go`    |
| Rust    | `curl ... | sh`               | `cargo run`            |
| Node.js | `curl ... | sudo bash -`      | `node app.js`          |
| Ruby    | `apt install ruby-full`       | `ruby script.rb`       |
| PHP     | `apt install php`             | `php script.php`       |
| .NET    | `wget ...`                    | `dotnet run`           |
| Haskell | `apt install ghc`             | `ghc hello.hs`         |

不同Linux发行版的包管理工具：
- **Ubuntu/Debian**: `apt`
- **CentOS/RHEL**: `yum` / `dnf`
- **Arch Linux**: `pacman`
