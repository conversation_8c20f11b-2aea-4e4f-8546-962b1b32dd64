### 2025/8/21日报

**今日工作内容**：

1. 持续整理种子下载进度，更新表格，统计进度，将其他方式下载的文献文件上传至软路由存储
2. 完成 EAM 系统相关工作：
   - 将 EAM 系统镜像上传至私有仓库，方便后续统一部署与版本管理。
   - 移除 Redis 模块，优化整体架构并整理目录结构。
   - 对 docker-compose 架构进行梳理，目前包含 5 个主要服务：
     - **MySQL 数据库**（持久化存储）
     - **对象存储 MinIO**
     - **主应用 App**
     - **BPM 应用 App**
     - **反向代理 Nginx**
   - 对上述服务进行主要配置与整理，包括配置文件优化、目录挂载、数据库持久化、端口映射等。
3. 协助其他工程师解决端口转发问题，

**后续计划**：

- 准备 EAM 系统的数据备份方案，确保业务数据安全与可恢复性。
- 持续完善容器化架构文档及服务配置细节。