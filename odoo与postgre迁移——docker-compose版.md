### odoo与postgre迁移——docker-compose版

#### 一、创建数据库目录

```bash
mkdir /opt/odoo-db
mkdir -p /opt/odoo-db/data    #数据库持久化目录
mkdir -p /opt/odoo-db/backup  #数据库备份目录，将数据库备份文件放入这个目录
```

#### 二、创建网络

```bash
docker network create odoo-net
```

#### 三、创建docker-compose

```yml
#docker-compose.yml
version: "3.9"

networks:
  odoo-net:
    external: true

services:
  odoo-db:
    image: registry.cn-shanghai.aliyuncs.com/sucloud/postgres:14
    container_name: odoo-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: admin@123
      TZ: Asia/Shanghai
    volumes:
      - /opt/odoo-db/data:/var/lib/postgresql/data
      - /opt/odoo-db/backup:/backup
    ports:
      - "5432:5432"
    networks:
      - odoo-net
```

#### 四、启动

```bash
docker compose up -d
```

```bash
#检查是否启动
docker ps -a 
```

#### 五、进入容器恢复备份

```bash
docker exec -it odoo-db bash
ls /backup/odoo_backup.dump
PGPASSWORD=admin@123 pg_restore -U odoo -d odoo /backup/odoo_backup.dump
```

#### 六、验证数据库是否恢复

```bash
#1.进入容器
docker exec -it odoo-db bash
#2.切换用户
psql -U odoo -d odoo
#3.查看数据库有哪些表
\dt
#4.删除所有 store_fname 不为空的记录，不管这个文件在 filestore 里存不存在。启动odoo后在执行。
DELETE FROM ir_attachment WHERE store_fname IS NOT NULL;
```

#### 七、迁移odoo，解压odoo

```
cd /opt
tar xzvf odoo.tar.gz
```

#### 八、进入odoo目录

```bash
cd /opt/odoo
```

#### 8.1、修改配置文件

```bash
vim /opt/odoo/etc/odoo.conf

#db_host = localhost   #改为容器名 odoo-db
db_host = odoo-db
```

#### 九、创建docker-compose文件

```yml
#docker-compose.yml
version: "3.9"

networks:
  odoo-net:
    external: true

services:
  odoo-server:
    image: registry.cn-shanghai.aliyuncs.com/sucloud/my-odoo:18.0
    container_name: odoo-server
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
      # 数据库连接
      DB_HOST: odoo-db    # 这里填 PostgreSQL 容器名
      DB_PORT: 5432
      DB_USER: odoo
      DB_PASSWORD: admin@123
      DB_NAME: odoo
    volumes:
      - /opt/odoo:/opt/odoo
    ports:
      - "8069:8069"
    networks:
      - odoo-net
```

#### 十、启动并检查容器是否正常启动

```bash
docker compose up -d
docker ps -a 
```

#### 十一、浏览器验证

```
http://ip:8069
```

#### 十二、后续可选，nginx代理多个服务

**创建nginx目录**

```bash
mkdir /opt/nginx
```

**创建docker-compose文件**

```yml
#docker-compose.yml
version: "3.9"

networks:
  odoo-net:
    external: true
  foxnic_jK35_network:
    external: true

services:
  nginx:
    image: nginx:1.27
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /opt/nginx/conf.d:/etc/nginx/conf.d:ro
      - /opt/nginx/certs:/etc/nginx/certs:ro
      - /opt/nginx/logs:/var/log/nginx
    networks:
      - odoo-net
      - foxnic_jK35_network
```

**创建nginx代理配置文件**

```nginx
server {
    listen 80;
    server_name ashersgf.xyz;

    # --------------------------
    # 根路径代理 Odoo
    # --------------------------
    location / {
        proxy_pass http://odoo-server:8069/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 支持 WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";

        # 禁止 Nginx 自动修改重定向
        proxy_redirect off;
    }

    # -------------------------
    # 假设 EAM 服务在 /eam/ 路径
    # --------------------------
#    location /eam/ {
#        proxy_pass http://app_app:8089/;
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;
#
#        proxy_http_version 1.1;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "Upgrade";
#
#        # 如果服务不支持子路径访问，可使用 URL 重写
#        # proxy_redirect / /eam/;
#    }
}
```

**https协议配置文件**

```nginx
# --------------------------
# HTTP 自动跳转到 HTTPS
# --------------------------
server {
    listen 80;
    server_name ashersgf.xyz;

    # 所有 HTTP 请求重定向到 HTTPS
    return 301 https://$host$request_uri;
}

# --------------------------
# HTTPS 主配置
# --------------------------
server {
    listen 443 ssl;
    server_name ashersgf.xyz;

    # SSL 证书路径
    ssl_certificate /etc/nginx/ssl/ashersgf.xyz.crt;         # 替换为实际证书路径
    ssl_certificate_key /etc/nginx/ssl/ashersgf.xyz.key;     # 替换为实际私钥路径
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # --------------------------
    # Odoo 子路径代理
    # --------------------------
    location / {
        proxy_pass http://odoo-server:8069/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
    }

    # --------------------------
    # EAM 子路径代理
    # --------------------------
#    location /eam/ {
#        proxy_pass http://app_app:8089/;
#        proxy_set_header Host $host;
#        proxy_set_header X-Real-IP $remote_addr;
#        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#        proxy_set_header X-Forwarded-Proto $scheme;

#        proxy_http_version 1.1;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "Upgrade";
#    }

```

**PS：如果用https协议的配置文件，docker-compose需要修改，挂载宿主机的ssl证书路径**

#### 十三、备份方案

**方案一、备份持久化目录**

```bash
/opt/odoo-db/data     #仅备份这个目录，是数据库的持久化目录，包含所有postgresql的文件
```

**方案二、只备份数据库**

```bash
#备份odoo库，备份脚本如下，
#odoo-db-backup.sh
#!/bin/bash

# 配置参数
BACKUP_DIR="/opt/odoo-db/backup"
CONTAINER_NAME="odoo-db"
DB_NAME="odoo"
DB_USER="odoo"
DB_PASSWORD="admin@123"
DATE=$(date +"%Y%m%d%H%M%S")
BACKUP_FILE="${BACKUP_DIR}/odoo_backup_${DATE}.dump"
BACKUP_FILE_COMPRESSED="${BACKUP_FILE}.gz"
RETENTION_DAYS=30

# 获取容器的数据库密码
export PGPASSWORD=$DB_PASSWORD

# 使用 pg_dump 备份数据库
docker exec ${CONTAINER_NAME} pg_dump -U ${DB_USER} -F c -b -v -f /backup/odoo_backup_${DATE}.dump ${DB_NAME}

# 移动备份文件到宿主机的备份目录
docker cp ${CONTAINER_NAME}:/backup/odoo_backup_${DATE}.dump ${BACKUP_FILE}

# 删除容器内的临时备份文件
docker exec ${CONTAINER_NAME} rm /backup/odoo_backup_${DATE}.dump

# 压缩备份文件
gzip ${BACKUP_FILE}

# 删除超过30天的备份文件
find ${BACKUP_DIR} -type f -name "odoo_backup_*.dump.gz" -mtime +${RETENTION_DAYS} -exec rm -f {} \;

# 打印备份结果
echo "Backup completed and compressed: ${BACKUP_FILE_COMPRESSED}"
```

**设置定时任务**

```bash
crontab -e
0 2 * * * /path/to/backup.sh     #每天凌晨2点执行备份
```

**添加权限**

```bash
chmod +x /opt/odoo-db/backup.sh
```

