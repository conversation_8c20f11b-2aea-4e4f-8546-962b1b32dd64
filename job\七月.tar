七月/                                                                                             0000777 0000000 0000000 00000000000 15042626055 010105  5                                                                                                    ustar                                                                                                                                                                                                                                                          七月/2025710日报.md                                                                             0000666 0000000 0000000 00000002544 15033712022 013331  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/10日报

**工作内容：**

1. **数据库备份检查与排查：**
   - 完成了日常数据库备份的检查，确保备份任务正常执行。
   - 排查了Linux机器卡死问题，进行了相关分析并找到了潜在的原因。
2. **文献下载平台优化：**
   - 对文献下载平台进行了优化，增强了PDF解析能力，提升了解析的准确性和成功率。
   - **Unpaywall API集成与页面爬取：** 完成了对Unpaywall API的集成，并增强了页面爬取的兜底机制。支持查找a、link、button、iframe、embed等标签中的PDF下载入口，确保了更多文献能够被抓取。
3. **Sci-Hub镜像增强：**
   - 集成了20多个最新的Sci-Hub镜像，作为最后的兜底方案，确保了即便在无法直接通过其他渠道下载时，也能通过Sci-Hub镜像进行访问和下载。
   - 自动遍历所有可用的Sci-Hub镜像，确保下载不中断。
4. **DOI格式兼容性优化：**
   - 现在支持用户输入多种格式的DOI，如标准DOI、带前缀DOI（如doi:、https://doi.org/等）、以及期刊官网URL（如science.org、wiley.com等），系统能够自动提取标准DOI，免去用户手动转换的麻烦。
5. **PDF解析测试：**
   - 测试了大部分PDF文献的解析功能，成功率大幅提升，系统能够顺利解析大部分文献。                                                                                                                                                            七月/2025711日报.md                                                                             0000666 0000000 0000000 00000003214 15034167353 013341  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/11日报

**工作内容：**

1. **数据库备份与日志检查：**
   - 完成了日常数据库备份检查，确保备份任务正常执行；
   - 对系统日志进行了检查，确认没有异常情况。
2. **系统卡死问题分析：**
   - 初步分析了系统卡死的原因，发现问题主要与网络中断及其集中在一个 CPU 核心上有关；
   - 网卡驱动或硬件崩溃可能是导致此问题的根本原因；
   - 根据排查，怀疑是 BIOS/内核无法正确控制 PCIe 电源管理（ASPM），导致网卡硬件挂起、网络完全失联，并且在某些情况下 HDMI 和 VGA 显示也出现死机现象，可能是 PCIe 总线锁死导致整个 I/O 总线卡死；
   - 采取的临时措施：禁用 PCIe 电源管理，进一步观察效果，使用命令 `grubby --update-kernel=ALL --args="pcie_aspm=off"` 禁用电源管理。
3. **DOI 解析 PDF 失败分析：**
   - 分析了 DOI 解析 PDF 失败的原因，主要问题是目标 DOI 的反爬机制，导致解析失败；
   - 其他可能原因：
     - DOI 页面需要登录才能下载 PDF；
     - DOI 指向的 PDF 链接只能在线查看；
     - 直接链接的 DOI 可能会被重定向；
   - 对策：考虑使用多个 Sci-Hub 进行解析，同时尝试通过学校 VPN 代理绕过反爬机制。
4. **定时重启设置：**
   - 根据机器卡死的情况，计划设置定时重启来确保系统稳定性，使用 `cron` 配置任务，定期重启系统。

**待办事项：**

- 继续观察禁用 PCIe 电源管理后的效果；
- 配置定时重启任务，确保系统自动重启以避免挂死。                                                                                                                                                                                                                                                                                                                                                                                    七月/2025714日报.md                                                                             0000666 0000000 0000000 00000004535 15035161673 013353  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/14日报

**工作内容**:

1. **软路由恢复下载任务**：
   - 由于软路由存储过热宕机，导致下载任务中断。今天对其进行了恢复，确认任务已成功恢复，下载正常进行。
   - 进行了软路由的温度监控与存储健康检查，优化了任务恢复流程，避免类似问题再次发生。
2. **Odoo管理与使用**：
   - 深入了解了 Odoo 的管理与使用，特别是社区版 18 的配置与功能。
   - 重点学习了如何高效管理 Odoo 系统，包括如何配置多用户环境、操作日志监控等内容。
   - 进行了系统调优，确保 Odoo 的稳定性和运行效率。
3. **虚拟环境与 systemd 服务管理**：
   - 对 Odoo 使用的虚拟环境进行了重新管理，确保与现有环境的兼容性。
   - 对 systemd 服务进行了优化配置，确保 Odoo 服务能够在服务器重启后自动启动，提升系统可用性。
4. **PostgreSQL数据库备份脚本**：
   - 编写了 PostgreSQL 数据库备份脚本，确保定期备份数据库以防数据丢失。
   - 优化了备份脚本的执行效率，并添加了备份状态通知机制，及时提醒系统管理员备份结果。
5. **Odoo 会议功能权限问题**：
   - 发现 Odoo 中的会议功能由于 HTTP 协议限制，无法开启麦克风和视频等权限。针对这一问题：
     - 通过 OpenSSL 创建了安全证书，配置了 HTTPS。
     - 搭建了 Nginx 反向代理，使用 HTTPS 重定向解决了权限问题。
   - 进行了多次测试，确保会议功能在 HTTPS 环境下能够正常开启视频与音频权限。
6. **问题排查与系统优化**：
   - 对 Odoo 系统进行了常规的健康检查，包括查看错误日志、数据库性能优化和缓存配置等，确保系统高效稳定运行。

**本周后续计划**:

- **继续研究 Odoo 高级功能**：将继续深入了解 Odoo 的权限管理、报告系统、模块等功能。
- **完成 Odoo 权限与用户管理配置**：特别是多用户角色和权限配置方面，以提高团队协作效率。
- **系统优化与监控**：进一步优化 Odoo 系统的性能，搭建更完善的监控机制，确保系统的健康运行。
- **备份与恢复策略**：完善 PostgreSQL 数据库的备份与恢复策略，确保系统在灾难发生时能够快速恢复。                                                                                                                                                                   七月/2025715日报.md                                                                             0000666 0000000 0000000 00000002703 15035407774 013354  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/15日报

**工作内容：**

1. **软路由网络管理：**
   - 将所有软路由设备集中放在一个位置，并通过交换机连接网络，确保设备之间的顺畅通信与管理。
2. **Odoo系统学习：**
   - 继续深入了解Odoo系统的各模块，重点关注以下模块：
     - **员工管理：** 学习如何创建和管理员工档案，设置角色与权限。
     - **发票管理：** 探索如何创建、处理和管理客户发票，熟悉相关的财务流程。
     - **项目管理：** 了解如何跟踪项目的进度、分配任务及资源，确保项目顺利进行。
     - **客户关系管理（CRM）：** 学习如何管理客户信息、销售机会及销售过程。
     - **采购管理：** 探索如何创建采购订单、跟踪供应商、管理采购流程。

**参考资源：**

- [Odoo官方文档](https://www.odoo.com/zh_CN/page/docs)
- [OdooAI文档](https://www.odooai.cn/documentation/18.0/zh_CN/index.html)
- [Odoo官方教程视频](https://www.bilibili.com/video/BV1eC4y1p7Xp?vd_source=13b1f5ef751f0f34c50671507f2a34a9)

**问题与挑战：**

- 目前对Odoo的一些高级配置和权限管理功能还不够熟悉，

**明日计划：**

- 深入学习Odoo中权限管理与工作流的设置，特别是如何通过权限控制员工访问不同模块。
- 完成Odoo系统的更多实践操作，特别是在发票和采购模块上进行实际配置。                                                             七月/2025716日报.md                                                                             0000666 0000000 0000000 00000003140 15035653406 013344  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/16日报

**今日完成工作：**

1. **Odoo 系统深入学习与模块熟悉：**
   - 继续深入了解 Odoo 系统的使用方式，重点关注以下模块的功能与管理流程：
     - 采购管理模块（采购订单流程、供应商管理等）
     - 用户与员工模块（权限管理、用户创建等）
     - 发票与客户模块（基础操作和流程梳理）
   - 初步理清了各模块之间的联动关系，为后续的实际使用和培训打下基础。
2. **邮件服务器测试与排查：**
   - 配置 Odoo 的邮件发送服务器，测试包括使用 163 邮箱作为 SMTP 发送源。
   - 识别邮件发送失败原因（如发件人验证失败等），并进行针对性排查和日志分析。
   - 为后续实现用户注册和通知功能做好基础准备。
3. **软路由断电后的下载任务恢复：**
   - 对软路由断电后下载状态进行恢复操作。
   - 验证 Aria2 的断点续传功能，确保大文件下载任务能在系统重启后自动接续，避免数据丢失。
   - 检查存储挂载和服务自启配置，增强系统稳定性。

**遇到问题及处理：**

- 邮件服务器 SMTP 配置存在验证限制（如需发件人地址与认证用户一致），已确认配置要求并调整测试参数。
- 软路由存储因断电未正常挂载，重新检查挂载点及服务启动顺序，目前状态已恢复正常。

**明日计划：**

- 继续完成 Odoo 发票、项目、采购等模块的配置演练与使用测试；
- 尝试集成邮件接收功能，确保系统双向通信能力。                                                                                                                                                                                                                                                                                                                                                                                                                                七月/2025717日报.md                                                                             0000666 0000000 0000000 00000005434 15036145401 013345  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/17日报

### 一、今日工作内容

1. **Odoo 系统深入学习与配置优化**
   - 继续深入学习 Odoo 的模块使用，包括：项目管理、采购管理、发票处理、客户关系管理与员工模块；
   - 测试统一发件服务器配置，使用 163 邮箱 SMTP 服务器进行发件功能测试，定位并解决 “`Mail from must equal authorized user`” 错误；
   - 初步整理了后续 Odoo 使用与汇报重点方向，包括发票开具、项目流程追踪、员工信息维护与客户联络管理。
2. **ComfyUI 部署与基础使用**
   - 成功在服务器上完成 ComfyUI 的部署，使用 Python 虚拟环境搭建并通过 `systemd` 实现服务化管理；
   - 将服务监听端口配置为 8188 并设置为 0.0.0.0，确保局域网用户可访问；
   - 学习 ComfyUI 的基本工作流及用途，明确其作为图像生成与 AI 流程编辑平台的应用场景；
   - 计划后续结合文生图、任务调度、Web 操作台等功能展开使用实验。
3. **种子任务整合与去重管理**
   - 收集整理所有软路由（共计 5 台）当前下载完成的种子任务目录；
   - 对已在其他设备成功下载的种子任务进行本地去重处理，统一执行**暂停并删除操作**，有效避免重复下载和资源浪费；
   - 制作种子下载进度清单表格，准备可视化进度记录页面，便于统一管理；
   - 初步规划后续自动化脚本，实现种子任务定时扫描、状态比对与去重动作。

------

### 二、存在问题与解决措施

| 问题描述                                    | 处理结果                                                 |
| ------------------------------------------- | -------------------------------------------------------- |
| Odoo 发件人与授权用户不一致导致邮件发送失败 | 修改发件人配置，确保与 SMTP 认证账号一致，问题已解决     |
| ComfyUI 使用不熟悉，功能定位模糊            | 已完成部署与测试运行，后续计划编写入门手册并探索实际用途 |
| 多路由设备存在重复下载任务，消耗存储与带宽  | 通过目录比对与人工确认，完成重复任务清理，提升系统效率   |



------

### 三、明日工作计划

- 完善 Odoo 模块测试流程，尝试实际数据流转（如采购 → 发票 → 项目）；
- 编写 ComfyUI 使用流程说明，测试常见 AI 图像生成工作流；
- 制作统一种子任务管理 Excel 表，加入“下载状态”、“归属路由”、“更新时间”等字段；
- 开始开发自动化种子去重脚本，计划配合 cron 定时运行；
- 若时间允许，计划尝试将 ComfyUI 部分功能与软路由任务结合（如生成封面图、批量截图等）。                                                                                                                                                                                                                                    七月/2025718日报.md                                                                             0000666 0000000 0000000 00000003766 15036601111 013347  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/18日报

**今日工作内容**:

1. **今日工作内容**:
   1. **种子下载进度整理**：
      - 完成了种子下载进度的整理和填写工作，将各软路由的下载进度详细记录，确保下载状态清晰可见。
      - 在进度记录表中增加了标记已完成下载的目录，并通过颜色区分已完成和未完成任务，提升可视化效果。
   2. **Odoo学习与二次开发**：
      - 持续深入学习 Odoo 的项目管理模块，并进行二次开发以满足特定需求。
      - 现有的 Odoo 项目模板无法满足项目管理的需求，因此进行了二次开发，初步完成了一个符合要求的项目模板例子。
      - 已对模板字段进行了定制，确保模板能适应不同项目类型的需求，下一步将进行更精细的调试和完善。
   3. **ComfyUI调研与应用**：
      - 继续调研 ComfyUI，了解其在图形用户界面中的应用和优势。
      - 初步评估其在项目中如何整合，以便更好地提升用户交互体验。
   4. **Podman技术调研**：
      - 对 Podman 进行了详细的技术调研，分析了 Podman 与 Docker、Containerd、K8s 的区别及优缺点。
      - 撰写了《容器技术方案对比》文档，清晰地列出了每种技术的适用场景、优势和局限性。
      - 结合目前的项目需求，评估 Podman 在资源管理和性能上的优势，准备与团队进一步讨论是否将其作为未来的容器管理解决方案。
   5. **K8s优缺点分析**：
      - 针对 Kubernetes (K8s) 的使用和部署进行了深入分析，尤其是针对高并发和多显卡的工作负载情况。
      - 撰写了 K8s 的优缺点对比分析，帮助团队更好地理解在当前架构中应用 K8s 的利与弊。
      - 讨论了是否采用 K8s 来优化现有的容器编排和自动化管理。
   6. **校内应用移植协作**：
      - 协助同事推进校内应用移植工作，参与讨论应用迁移的工作          七月/202571日报.md                                                                              0000666 0000000 0000000 00000002072 15030734101 013244  0                                                                                                    ustar                                                                                                                                                                                                                                                          #### 2025/7/1日报

**2025年7月1日 工作内容总结**

1. **数据库备份检查**
   
   - 完成日常数据库备份任务检查；
   - 核查备份文件的生成时间和内容完整性，未发现异常；
   - 同步检查相关日志，确认备份过程无报错。
   
2. **文献下载 Web 应用优化**

   - 为文献下载 Web 系统引入代理功能；
   - 修复直连代理方式下访问异常的问题；
   - 增加代理失效时的异常处理逻辑，避免任务错误下载或失败。

3. **网络与功能验证**

   - 前往学校进行现场测试；
   - 验证校园网下代理服务的连通性；
   - 测试 Web 下载功能在实际组网环境中的可用性，确保系统端到端流程正常。

4. **脚本维护与功能增强**

   - 修改自动认证脚本，提升在特定网络环境下的兼容性；
   - 新增测试网络连通性的脚本，便于快速排查代理或连接异常；
   - 编写并部署 Web 服务的开机自启脚本，确保系统重启后可自动恢复运行。

                                                                                                                                                                                                                                                                                                                                                                                                                                                                         七月/2025721日报.md                                                                             0000666 0000000 0000000 00000003344 15037404135 013341  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/21日报

### 一、今日已完成工作：

1. **校内移植软件部署：**
   - 在新配置的 Windows 环境中，重新安装校内移植的应用
2. **新同事系统初始化支持：**
   - 帮助新入职同事完成 Windows 系统重装；
3. **种子下载任务进度统计：**
   - 收集并整理当前所有 Aria2 种子任务的下载状态；
   - 更新至统一的进度表中，标记完成与未完成项，方便后续跟进与分流；
4. **DOI PDF 下载平台优化：**
   - 修改 DOI 解析与下载逻辑，新增自动提取 PDF 元信息功能（如标题、作者、期刊名称、出版年等）；
   - 优化异常处理机制，对无效 DOI、非 PDF 页面进行自动识别并提示用户；
   - 进行多轮本地测试，确保功能稳定。
5. **配合潘老师部署新版代码：**
   - 与潘老师配合完成文献平台后端新功能上线部署；
   - 测试新版代码的运行效果，包括任务分发、PDF 下载及元信息提取；
   - 记录部署中发现的问题并协助调整部署策略。
6. **Odoo 项目模板整理与二次开发对接：**
   - 对现有 Odoo 项目模块进行二次开发内容梳理，整理字段定义、模板结构和自定义功能；
   - 初步编排出适配当前业务需求的项目模板，涵盖项目名称、进度、负责人、分类等关键字段；
   - 为后续 Odoo 系统迁移与批量导入功能打好基础；
7. **MySQL 数据库定时备份排查与处理：**
   - 检查发现 MySQL 定时备份任务未执行（可能由于计划任务失效或脚本路径变更）；
   - 手动执行备份脚本完成当日备份操作，确认备份数据完整性；
   - 定位问题为 系统卡死。                                                                                                                                                                                                                                                                                            七月/2025722日报.md                                                                             0000666 0000000 0000000 00000002660 15037660646 013355  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/22日报

**工作内容**：

1. **种子下载持续统计**：
   - 完成了对种子的持续下载监控，确保了下载任务的稳定性和进度。
   - 在下载过程中，观察到使用**迅雷**（Thunder）在**Windows**系统上下载速度较快，但将下载文件传输到**软路由**存储时速度较慢。这可能与网络条件或软路由的I/O性能有关。
2. **DOI下载进度监控**：
   - 对**潘老师**提供的6,000多条**DOI**进行下载进度的观察和记录，确保所有条目都在顺利下载。
3. **文件传输方法总结**：
   - 探索并总结了以下几种文件夹导出到本地的方法：
     - **解压后传输到本地**：优先采用此方式，能确保文件完整传输并便于后续处理。
     - **使用SCP命令直接传输**：适用于快速传输，但需要考虑网络稳定性。
     - **直接下载**：适用于小文件，效率较高，但在大文件传输时可能存在限制。
     - 对文件夹访问目录增加了pdf文件数量检查，增加了总大小展示，增加了打包下载所有PDF的功能，
4. **容器技术（Podman）命令测试**：
   - 对**Podman**进行了命令测试，发现其命令与**Docker**大致相同，唯一的区别是**Podman**不需要守护进程，这为系统资源的优化提供了便利。

**待办事项**：

- 继续跟踪DOI下载进度，确保高效完成任务。

                                                                                七月/2025723日报.md                                                                             0000666 0000000 0000000 00000001770 15040122607 013337  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/23日报

## 工作内容

- **软路由下载任务恢复**
   由于昨晚疑似断电导致软路由上的下载任务中断，今天进行了全面恢复和状态核查。目前无新增下载任务，所有既有任务均已恢复正常，软路由系统运行稳定，下载进度持续推进。
- **容器GPU技术学习与ComfyUI使用**
   继续深入研究容器环境下GPU的配置与使用方法，掌握如何在容器中高效调用显卡资源，为后续GPU加速相关应用做准备。
   同时学习ComfyUI的使用流程，探索其在图形界面下进行模型推理和任务管理的能力，逐步建立起实操经验。
- **DOI文献下载进度跟踪**
   持续关注校内DOI文献下载任务的执行情况，
- **校内监控平台异常排查**
   监控校内移植的监控平台状态，出现异常及时反馈并处理
- **种子下载进度整理**
   继续整理并统计种子下载任务的整体进度和状态，确保数据准确完整。

        七月/2025724日报.md                                                                             0000666 0000000 0000000 00000001027 15040577651 013350  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/24日报

**下载进度统计**
 继续进行日常的下载进度统计工作，保持对下载任务的监控。

**Odoo模块开发**
 完成了Odoo新增项目模块的初步开发，模块实现了项目创建时可以设置自定义模板字段，并且在模板中能够查看这些字段。
 目前遇到的问题：

- 无法切换数据库功能。
- 模块功能尚不完整，存在一些未完成的部分。
   计划：明天将重点解决数据库切换功能的问题，并完善模块功能。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         七月/2025725日报.md                                                                             0000666 0000000 0000000 00000002232 15040642242 013335  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/25日报

### 今日完成事项：

1. **DOI文献下载任务整理与归档：**
   - 完成对六千多条 DOI 文献的下载任务。
   - 将所有下载文件整理导出至本地并同步至移动存储设备。
   - 包括：已下载文件、元数据 CSV 文件、下载失败列表以及完整日志记录文件，确保下载数据可追溯、可审查。
2. **Odoo自研模块开发与问题修复：**
   - 解决昨日 Odoo 模块中报表报错及数据库切换功能缺失的问题。
   - 当前已实现项目模块的完整功能，包括自定义模板字段展示和切换多数据库环境下的兼容性。
   - 完成对模块展示效果的调试和优化。
3. **种子任务持续整理与下载统计：**
   - 继续对现有种子下载进度进行统计与补全，优化数据展示方式，确保进度透明可查。
4. **未来迁移规划梳理：**
   - 对 Odoo 二次开发相关模块、数据结构等内容进行整理，提前准备未来的系统迁移工作。
   - 初步梳理 PostgreSQL 与 MySQL 数据库之间的数据结构与迁移策略，为后续数据库切换与兼容性测试做准备。                                                                                                                                                                                                                                                                                                                                                                      七月/2025728日报.md                                                                             0000666 0000000 0000000 00000003272 15041647046 013355  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/28日报

**一、今日工作内容**

1. **持续整理种子下载进度**
   - 对当前软路由上的种子下载任务数据进行了持续统计与整理。
2. **项目模板的全面修改与数据导入**
   - 针对项目管理需求，重新设计并优化了项目模板结构，增加了必要字段，提升数据完整性与可读性。
   - 使用 Odoo 的导入功能，将准备好的项目表格数据成功导入系统，完成项目实体的创建。
   - 验证导入数据的正确性及模板展示效果，确保操作体验良好。
3. **Docker 容器化部署准备工作**
   - 编写并调试 Odoo 服务的 Dockerfile，基于当前虚拟环境搭建环境，（包括 Python 依赖、Node.js、wkhtmltopdf 等）。
   - 设计了镜像构建流程，保证镜像体积和启动性能符合预期。
   - 完成初步的迁移文档撰写，详细记录了从源码环境迁移到 Docker 容器的步骤、注意事项及常见问题处理方案。
4. **数据库迁移方案整理**
   - 针对 PostgreSQL 与 MySQL 两大数据库，分别整理了完整的迁移方案，包含数据备份、用户权限迁移、数据恢复及验证流程。
   - 明确了与 Odoo 二次开发模块的依赖关系，确保迁移后数据库数据结构和权限配置保持一致，避免业务中断。
   - 准备相关脚本与操作指令

**二、存在问题及思考**

- Docker 镜像构建过程中，部分依赖包版本兼容性需进一步验证。
- 数据库迁移涉及权限和账号重建，需在迁移环境中多次测试，保证完整无遗漏。
- 迁移文档中对异常处理方案需要补充更详细的故障排查和恢复步骤。

                                                                                                                                                                                                                                                                                                                                      七月/2025729日报.md                                                                             0000666 0000000 0000000 00000003262 15042114651 013345  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/29日报

### 一、今日完成工作：

1. **Odoo及数据库容器化迁移测试**
   - 对现有的 Odoo 服务、PostgreSQL 数据库与 MySQL 数据库进行了完整的模拟迁移测试，验证容器部署可行性；
   - 使用 Docker 成功还原并部署所有服务，确保数据完整一致；
   - 手动构建 Odoo 镜像，并完成本地打包，以便后续在新环境中直接部署使用；
   - 整理并归档迁移流程与操作文档，包括 Odoo 服务、数据库迁移细节及依赖项说明。
2. **监控系统准备**
   - 鉴于后续对资源使用监控的需要，完成以下镜像的拉取并推送至私有镜像仓库：
     - Prometheus
     - Grafana
     - Node Exporter
     - cAdvisor
   - 初步规划容器部署后的资源使用监控方案，计划结合 Prometheus + Grafana 实现对宿主机与各容器 CPU、内存、网络等指标的可视化监控；
   - 整理监控配置部署说明文档，作为后续实施参考。
3. **软路由种子下载进度整理**
   - 继续统计整理各软路由节点的种子下载进度，包括成功下载记录、失败日志与元数据 CSV 文件；

------

### 二、后续工作计划：

- 在新环境中正式部署 Odoo、数据库服务与资源监控组件；
- 测试容器资源限制配置（如 CPU 限制、内存限制等）对实际服务运行的影响；
- 搭建完整的 Prometheus + Grafana 监控体系，包含容器自身指标与宿主机层面指标；
- 整合软路由下载进度数据到 Odoo 模块或可视化系统中，以实现统一管理与展示；
- 持续优化镜像构建与部署流程，提升迁移效率与稳定性。                                                                                                                                                                                                                                                                                                                                              七月/202572日报.md                                                                              0000666 0000000 0000000 00000002121 15031166335 013251  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/2日报

#### 一、今日完成工作：

1. **数据库备份与日志检查**
   - 对服务器进行日常数据库备份检查；
   - 确认所有定时备份任务正常运行，备份文件完整无误；
   - 检查并归档数据库日志，确认无异常报错记录。
2. **软路由环境部署与脚本优化**
   - 对已测试通过的软路由设备进行配置脚本整理；
   - 部署并验证 Web 服务在软路由中运行正常；
   - 安装 Python 环境，并编写脚本实现自动化任务；
   - 配置启动项与定时任务，确保系统重启后服务正常运行；
   - 所有部署项均完成测试，运行稳定。
3. **异地互联方案设计（易有云）**
   - 基于“易有云”工具设计两套异地互联解决方案，适用于不同网络环境：
     - **方案一**：基于 Windows 中转访问，支持本地与远端数据互通；
     - **方案二**：软路由端端口映射+易有云远程转发，实现局域网访问远端节点；
   - 分析各方案优缺点，完成文档撰写与验证测试。                                                                                                                                                                                                                                                                                                                                                                                                                                               七月/2025730日报.md                                                                             0000666 0000000 0000000 00000004443 15042354211 013335  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/30日报

### 一、今日完成工作内容

#### 1. **MySQL 容器配置与迁移完善**

- 调整 MySQL 容器启动方式，**加载自定义配置文件** `my_custom.cnf`，实现以下功能：
  - 开启 **通用查询日志**
  - 开启 **错误日志**
  - 开启 **慢查询日志**
- 挂载相关日志路径到宿主机指定目录，实现日志持久化存储和外部访问。
- 整理并完成 **MySQL 容器完整迁移文档**，涵盖内容如下：
  - 自定义配置文件设计与加载方法
  - 容器中数据库备份与恢复流程（包含备份压缩、导入等）
  - 数据库账号及权限恢复方法
  - 数据存储持久化策略（包括挂载路径说明）

#### 2. **Odoo 镜像优化与构建**

- 对现有 Odoo 镜像进行了**精简与优化**，重构 Dockerfile：
  - 合并依赖安装步骤，缩小镜像体积
  - 统一设置环境变量，简化部署参数
- 构建了一个更完整的自定义 Odoo 镜像，具备以下特性：
  - 包含全部必要依赖（Python、Node.js、wkhtmltopdf 等）
  - 内嵌当前生产环境 Odoo 源码目录及配置文件
  - 预加载所有自定义模块，镜像启动即为部署状态

#### 3. **后续工作准备**

- **资源监控与资源限制**：
  - 计划在服务迁移完成后，使用 Prometheus + Grafana 持续观察 Odoo 和 MySQL 的容器资源消耗（CPU、内存、I/O 等）
  - 制定初步资源限制策略，避免资源占满影响其他服务
- **备份与日志脚本容器化**：
  - 计划将现有 MySQL 备份脚本、登录日志提取脚本**改为可在容器中执行的版本**
  - 支持定时任务执行（后续考虑结合 `cron` 容器或 `host cron + docker exec` 实现）

------

### 二、存在问题 / 风险点

- MySQL 容器的日志文件增长需控制，计划引入 logrotate 或分割机制。

------

### 三、明日工作计划

- 编写适用于容器的 MySQL 自动备份脚本与日志提取脚本，并测试其可用性。
- 开始编写 Prometheus 配置文件，用于集成 MySQL 和 Odoo 容器的资源监控指标。
- 尝试将现有服务迁移到 Kubernetes 集群环境中，验证服务正常性与数据一致性。
- 预研 MySQL 及 Odoo 容器的资源限制参数配置与实际效果。                                                                                                                                                                                                                             七月/2025731日报.md                                                                             0000666 0000000 0000000 00000002546 15042626055 013350  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/31日报

### 一、Odoo 系统介绍与演示

今日对三位行政同事进行了 Odoo 系统的基础使用培训，内容包括：

- Odoo 模块化架构介绍（如项目管理、人事、审批等模块的作用）
- 创建用户及用户管理流程演示
- 权限组与角色配置简要说明，帮助其理解角色间权限隔离
- 强调当前系统为测试用途，数据不保留，后台数据库已提前完成备份，仅保留导入的项目模板数据供熟悉使用流程

目的为提前熟悉后续将上线的新系统，降低未来迁移上线时的适应成本。

------

### 二、MySQL 容器环境下备份脚本优化

- 将原有物理机环境下的 MySQL 自动备份脚本适配为容器环境使用
- 使用 `docker exec` 结合定时任务实现自动备份指定数据库
- 已在当前容器环境下完成脚本测试，验证备份文件正确生成并可恢复

------

### 三、监控系统准备

- 已完成监控系统相关的 `docker-compose.yml` 编写
- 包括 Prometheus、Grafana、Node Exporter、cAdvisor 等服务
- 等待后续服务迁移完毕后同步部署并统一纳入监控体系

------

### 四、后续计划

- 持续推进 Odoo、MySQL、PostgreSQL 服务容器化迁移与测试
- 配合行政使用反馈调整权限和操作逻辑，提前为上线做好准备                                                                                                                                                          七月/202573日报.md                                                                              0000666 0000000 0000000 00000002135 15031433643 013256  0                                                                                                    ustar                                                                                                                                                                                                                                                          #### 2025/7/3日报

**今日工作总结：**

1. **系统卡死问题排查与修复**
   - 针对近期系统频繁卡死的问题进行了深入排查。
   - 重点检查了系统日志（`dmesg`、`syslog`、`journalctl`）、内存、CPU占用、磁盘I/O等指标；
   - 排查了可能引发卡死的服务、定时任务、网络连接等问题；
   - 初步修复部分潜在问题，系统运行恢复正常，后续将继续监控稳定性。
2. **FRP + 公网穿透方案实现**
   - 完成 FRP 穿透方案的部署与调试，实现软路由公网访问功能；
   - 成功穿透 3 个端口服务：
     - **iStoreOS 管理界面**
     - **文献下载 Web 界面**
     - **NAS 管理界面**
   - 已实现通过公网访问以上 3 个服务，无需内网 VPN；
   - 当前访问正常，后续持续观察连通性与稳定性。

**后续计划：**

- 持续观察系统运行情况，收集更多日志，定位潜在问题根因；
- 监测 FRP 公网穿透的稳定性与安全性，必要时优化配置；
- 规划后续的访问权限与安全加固措施。                                                                                                                                                                                                                                                                                                                                                                                                                                   七月/202574日报.md                                                                              0000666 0000000 0000000 00000001530 15031675431 013260  0                                                                                                    ustar                                                                                                                                                                                                                                                          #### 2025/7/4日报

#### 今日完成工作：

1. **上午会议汇报**
   - 汇报了当前工作进展，包括文献下载平台、下载管理、服务器方案等内容。
2. **文献下载平台优化**
   - 完善了文献下载平台功能：
     - 实现了下载进度实时展示；
     - 支持通过网页直接下载到本地；
     - 已成功部署至公网访问环境（基于 FRP + 公网端口转发）；
     - 完成了 Aria2 下载任务的统计功能，统计内容包括：
       - 总任务数
       - 已完成任务数
       - 下载速度
       - 下载进度百分比
3. **服务器方案制定**
   - 完成了服务器支撑方案，确保能够稳定支撑以下核心服务：
     - MySQL 数据库服务
     - Odoo 系统服务
   - 方案兼顾未来扩展性与当前性价比需求。                                                                                                                                                                        七月/202577日报.md                                                                              0000666 0000000 0000000 00000005044 15032714745 013272  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/7 日报

#### 今日工作内容

1. **软路由及存储恢复**

- 处理软路由和存储设备因过热自动关机的问题，完成设备重启和恢复。
- 成功恢复下载任务状态，保障下载不中断。

1. **文献下载代码优化**

- 优化了文献下载脚本，重点解决了 PDF 解析频繁失败的问题，提高下载成功率。
- 持续观察优化效果，确保代码稳定性。

1. **下载进度监控系统开发**

- 设计并实现基于 Web 端的 aria2 下载进度展示界面。
- 实现多台软路由的下载进度实时汇总，端口 5000 对外展示，便于集中监控。

1. **网络状态监控**

- 持续监控 frp 穿透及公网网络状态，保障远程访问的稳定性。
- 进行相关日志及流量分析，预防潜在网络风险。

1. **数据库运维**

- 定期检查数据库备份完整性，确保数据安全。
- 监控数据库日志，及时发现和处理异常。

1. **系统状态监控平台部署**

- 成功部署 Prometheus + Node Exporter + Grafana 监控平台，用于监控系统各项指标（CPU、内存、磁盘、网络等）。
- 配置基础监控面板，直观展示软路由及服务器运行状态，辅助后续运维工作。

1. **系统卡死问题排查与网卡驱动优化**

- 针对近期服务器频繁出现 CPU 飙升和系统卡死的问题，分析发现主要原因是网卡中断过于频繁，导致 CPU 负载异常、系统卡顿。
- 原因包括：网卡驱动 r8169 性能不佳、未开启中断节流、软中断集中在单核处理等。
- 采用 ELRepo 源安装并切换至 r8125 专用驱动，成功替换 r8169 驱动，提升中断处理效率。
- 调整中断绑定及软中断配置，缓解 CPU 负载问题，系统运行稳定性明显提升。

------

#### 遇到的问题及解决方案

- **文献 PDF 解析失败频繁**：通过优化解析逻辑并增强异常处理机制，显著降低失败率，目前持续观察中。
- **网卡中断导致 CPU 飙升与系统卡死**：通过更换高性能网卡驱动 r8125，并优化中断分配及处理机制，有效解决系统卡死问题。

------

#### 明日工作计划

- 继续监控文献下载代码稳定性，进一步提升解析成功率。
- 优化 Web 端下载进度展示界面，增加用户体验功能。
- 深入分析 frp 穿透网络性能，探索优化方案。
- 继续数据库日志分析，完善备份策略。
- 丰富 Grafana 监控面板指标，增加报警配置，提升监控覆盖面。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            七月/202578日报.md                                                                              0000666 0000000 0000000 00000001614 15033144571 013265  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/8日报

### 今日工作内容：

- 主要处理了Linux机器的卡死问题，已初步排查网卡驱动与中断负载方面的问题。
- 目前正在逐步排查中，尝试通过调整网卡中断分配来定位问题根源。
- 已对部分系统参数进行了调优，等待系统表现反馈。

### 问题与挑战：

- Linux 服务器在处理高并发任务时出现卡死现象，初步怀疑可能与网卡中断或驱动有关。
- 需要进一步验证网卡与系统负载的关系，以及中断负载优化对系统稳定性的影响。

### 明日工作计划：

- 继续排查网卡驱动与中断相关的问题，结合日志和监控工具进行深度分析。

- 根据需要调整IRQ分配与内核参数，测试优化效果。

- 如果问题依旧，计划进行硬件方面的检查，如更换网卡或进行更细致的硬件诊断。

                                                                                                                      七月/202579日报.md                                                                              0000666 0000000 0000000 00000003430 15033446035 013264  0                                                                                                    ustar                                                                                                                                                                                                                                                          ### 2025/7/9日报

- **工作内容**：

  1. **数据库备份与日志检查**：
     - 完成了对各个数据库的日常备份检查，确保备份任务按时执行，备份文件完好无损。
     - 核对了备份日志，排查了是否有异常信息，确保备份数据的完整性与可用性。
  2. **学校软路由组网环境维护**：
     - 进行了软路由网络的状态检查，确保各项路由转发正常，网络带宽符合预期。
     - 优化了frp（Fast Reverse Proxy）与公网转发配置，保证了外网服务的稳定性与高可用性。
  3. **上网认证掉线问题排查**：
     - 对校园网认证掉线问题进行了日志分析，识别出问题原因，并进行了相关脚本优化，增强了自动认证的稳定性。
     - 提供了解决方案并进行了部分测试，待后续观察效果。
  4. **文献下载平台优化**：
     - 优化了文献下载平台的PDF解析能力，新增了以下功能：
       - 原本先通过Unpaywall查找OA PDF，如果失败，直接爬取期刊页面，寻找所有疑似PDF链接。
       - 新增了常见出版社直链规则，自动拼接PDF直链（如Science、Nature、Cell、Springer、Wiley、IEEE等），提高了下载的准确率与成功率。
     - 对下载任务状态进行了监控，确保任务能按照设定优先级顺利完成。

  **问题及解决方案**：

  - 发现学校软路由中的部分转发规则配置不完全，导致部分服务外网不可访问。通过优化规则并更新frp配置，问题得到有效解决。

  **待处理事项**：

  - 继续监控软路由网络环境，确保外网转发不再受到影响。
  - 持续跟进文献下载平台的优化效果，收集用户反馈以进一步完善功能。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        