# 2025年7月工作月报 — 苏国峰

## 一、工作内容总结

### 第一周（7/1-7/7）：文献下载平台代理功能与组网穿透

- 为文献下载Web系统引入代理功能，修复直连代理方式下访问异常的问题；
- 增加代理失效时的异常处理逻辑，避免任务错误下载或失败；
- 前往学校进行现场测试，验证校园网下代理服务的连通性；
- 测试Web下载功能在实际组网环境中的可用性，确保系统端到端流程正常；
- 修改自动认证脚本，提升在特定网络环境下的兼容性；
- 编写并部署Web服务的开机自启脚本，确保系统重启后可自动恢复运行；
- 完成软路由设备配置脚本整理，部署并验证Web服务在软路由中运行正常；
- 基于"易有云"工具设计两套异地互联解决方案：
  - 方案一：基于Windows中转访问，支持本地与远端数据互通；
  - 方案二：软路由端端口映射+易有云远程转发，实现局域网访问远端节点；
- 分析各方案优缺点，完成文档撰写与验证测试。
- 完成 FRP 穿透方案的部署与调试，实现软路由公网访问功能；穿透三个端口：IStoreos管理界面，文献下载web界面，AriaNG页面
- 对Aria2下载任务的统计功能，web页面展示
- 配置基础监控面板，直观展示软路由及服务器运行状态

### 第二周（7/8-7/14）：系统故障排查与文献下载平台优化

- 深入排查Linux机器卡死问题，重点分析网卡驱动与中断负载方面的问题；
- 逐步排查网卡中断分配，尝试通过调整网卡中断分配来定位问题根源；
- 对部分系统参数进行了调优，调整IRQ分配与内核参数；
- **文献下载平台优化**：
  - 增强PDF解析能力，大幅提升解析准确性和成功率；
  - 完成Unpaywall API集成，增强页面爬取的兜底机制；
  - 支持查找a、link、button、iframe、embed等标签中的PDF下载入口；
  - 集成20多个最新的Sci-Hub镜像，作为最后的兜底方案；
  - 自动遍历所有可用的Sci-Hub镜像，
  - 优化DOI格式兼容性，支持多种格式DOI输入（标准DOI、带前缀DOI、期刊官网URL等）；
  - 系统能够自动提取标准DOI，免去手动转换的麻烦；
- 测试大部分PDF文献的解析功能，成功率大幅提升。

### 第三周（7/15-7/21）：Odoo系统学习与了解Comfyui

- 将所有软路由设备集中放在一个位置，并通过交换机连接网络，确保设备之间的顺畅通信与管理；
- 对 Odoo 使用的虚拟环境进行了重新管理，确保与现有环境的兼容性。
- 对 systemd 服务进行了优化配置，确保 Odoo 服务能够在服务器重启后自动启动，提升系统可用性。
- 发现 Odoo 中的会议功能由于 HTTP 协议限制，无法开启麦克风和视频等权限。针对这一问题：
  - 通过 OpenSSL 创建了安全证书，配置了 HTTPS。
  - 搭建了 Nginx 反向代理，使用 HTTPS 重定向解决了权限问题。
- 进行了多次测试，确保会议功能在 HTTPS 环境下能够正常开启视频与音频权限。
- **开始深入学习Odoo系统**，重点关注以下模块：
  - 员工管理：学习如何创建和管理员工档案，设置角色与权限；
  - 发票管理：探索如何创建、处理和管理客户发票，熟悉相关的财务流程；
  - 项目管理：了解如何跟踪项目的进度、分配任务及资源；
  - 客户关系管理（CRM）：学习如何管理客户信息、销售机会及销售过程；
  - 采购管理：探索如何创建采购订单、跟踪供应商、管理采购流程；
- 配置Odoo的邮件发送服务器，测试包括使用163邮箱作为SMTP发送源；
- 识别邮件发送失败原因（如发件人验证失败等），进行针对性排查和日志分析；
- 在服务器上完成 ComfyUI 的部署，使用 Python 虚拟环境搭建并通过 `systemd` 实现服务化管理；
- 学习 ComfyUI 的基本工作流及用途，明确其作为图像生成与 AI 流程编辑平台的应用场景；
- **Odoo项目模块二次开发**：
  - 发现现有Odoo项目模板无法满足项目管理需求，开始进行二次开发；
  - 初步完成符合要求的项目模板例子，对模板字段进行定制；
  - 确保模板能适应不同项目类型的需求；
  - 对现有Odoo项目模块进行二次开发内容梳理，整理字段定义、模板结构和自定义功能；
  - 初步编排出适配当前业务需求的项目模板，涵盖项目名称、进度、负责人、分类等关键字段；
- 对软路由断电后下载状态进行恢复操作；
- **校内应用移植协作**：对文物遗址远程监控移植
- 对新同事的电脑系统安装以及其他同事需要安装windows系统的需求

### 第四周（7/22-7/31）：服务迁移与容器技术研究

- **种子下载持续统计与监控**：
  - 完成对种子的持续下载监控，
  - 观察到使用迅雷在Windows系统上下载速度较快，但传输到软路由存储时速度较慢；
  
- **DOI文献大规模下载任务**：
  
  - 对潘老师提供的6,000多条DOI进行下载进度的观察和记录；
  - 确保所有条目都在顺利下载；
  
- **文件传输方法总结与优化**：
  - 解压后传输到本地：优先采用此方式，确保文件完整传输；
  - 使用SCP命令直接传输：适用于快速传输；
  - 直接下载：适用于小文件，效率较高；
  - 对文件夹访问目录增加了PDF文件数量检查，增加了总大小展示；
  - 增加了打包下载所有PDF的功能；
  
- **容器技术研究**：
  
  - 对Podman进行了命令测试，发现其命令与Docker大致相同；
  - 发现Podman不需要守护进程，为系统资源的优化提供了便利；
  
  **K8s优缺点分析**：
  
  - 针对 Kubernetes (K8s) 的使用和部署进行了深入分析
  - 编写了 K8s 的优缺点对比分析，更好地理解在当前架构中应用 K8s 的利与弊。
  
- **Odoo项目模块开发完成**：
  
  - 7/24完成Odoo新增项目模块的初步开发；
  - 模块实现了项目创建时可以设置自定义模板字段功能；
  - 在模板中能够查看这些自定义字段；
  - 遇到数据库切换功能问题，模块功能存在未完成部分；
  
  **需求大致完成**
  
  - 完成对六千多条DOI文献的下载任务；
  - 将所有下载文件整理导出至本地并同步至移动存储设备；
  - 包括：已下载文件、元数据CSV文件、下载失败列表以及完整日志记录文件；
  - 解决Odoo模块中报表报错及数据库切换功能缺失的问题；
  - 实现项目模块的完整功能，包括自定义模板字段展示和切换多数据库环境下的兼容性；
  - 初步梳理PostgreSQL与MySQL数据库之间的数据结构与迁移策略。
  
  **迁移测试**

- 对现有的Odoo服务、PostgreSQL数据库与MySQL数据库进行了完整的模拟迁移测试；
- 编写并调试 Odoo 服务的 Dockerfile，基于当前虚拟环境搭建环境，（包括 Python 依赖、Node.js、wkhtmltopdf 等）。
- 使用Docker成功还原并部署所有服务，确保数据完整一致；
- 手动构建Odoo镜像，并完成本地打包，以便后续在新环境中直接部署使用；
- 整理并归档迁移流程与操作文档，包括Odoo服务、数据库迁移细节及依赖项说明；

- 完成以下镜像的拉取并推送至私有镜像仓库：Prometheus、Grafana、Node Exporter、cAdvisor；
- 初步规划容器部署后的资源使用监控方案；
- 计划结合Prometheus + Grafana实现对宿主机与各容器CPU、内存、网络等指标的可视化监控；
- 完成监控系统相关的docker-compose.yml编写；

- 将原有物理机环境下的MySQL自动备份脚本适配为容器环境使用；
- 使用docker exec结合定时任务实现自动备份指定数据库；
- 已在当前容器环境下完成脚本测试，验证备份文件正确生成并可恢复；

- 权限组与角色配置简要说明，帮助其理解角色间权限隔离；
- 强调当前系统为测试用途，数据不保留，后台数据库已提前完成备份；
- 目的为提前熟悉后续将上线的新系统，降低未来迁移上线时的适应成本。
- 对三位行政同事进行了Odoo系统的基础使用培训；
- 调整 MySQL 容器启动方式，**加载自定义配置文件** `my_custom.cnf`，实现以下功能：
  - 开启 **通用查询日志**
  - 开启 **错误日志**
  - 开启 **慢查询日志**
- 对现有 Odoo 镜像进行了**精简与优化**，重构 Dockerfile：
  - 合并依赖安装步骤，缩小镜像体积
  - 统一设置环境变量，简化部署参数
- **linux机器进行一些优化以及内核参数调整，并且禁用了swap分区，调整ssh加速等，目前稳定运行几天没有出现卡死情况**

### 部分成果截图

![image-20250804184741930](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250804184741930.png)

![image-20250804184839027](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250804184839027.png)

![image-20250804184906236](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250804184906236.png)

![image-20250804184928560](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250804184928560.png)

![image-20250804184955032](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250804184955032.png)

![image-20250804185232980](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250804185232980.png)
