### 容器技术的对比与优缺点

### **1.1 Podman**

- **简介**：Podman 是一个开源的容器管理工具，与 Docker 类似，但不需要守护进程。支持与 Docker 命令兼容，能够更高效地管理容器。
- **特点**：
  - 无守护进程：避免使用守护进程，通过每个容器进程独立管理。
  - 本地用户命名空间：支持 rootless 模式，以非 root 用户运行容器，提高安全性。
  - 与 Docker 命令兼容：大多数 Docker 命令在 Podman 中都可以使用。

### **1.2 Docker**

- **简介**：Docker 是最流行的容器平台，能够将应用程序和其依赖打包到容器中，以便开发、测试、生产环境中使用。
- **特点**：
  - 守护进程管理：使用 Docker 守护进程进行容器管理。
  - 丰富的生态系统：拥有 Docker Hub 和其他工具，广泛用于 CI/CD 流程。
  - 强大的社区支持：Docker 是容器技术的“金标准”，拥有丰富的资源。

### **1.3 Containerd**

- **简介**：Containerd 是一个轻量级的容器管理系统，专注于容器生命周期的管理（如容器的创建、运行、停止等）。它是 Docker 的核心组件之一，但没有提供应用层的功能。
- **特点**：
  - 轻量级：专注于容器生命周期管理，不涉及应用功能。
  - 高度模块化：适合用作其他容器平台（如 Kubernetes）的基础设施组件。
  - 仅处理容器的基础管理，不包含部署与编排功能。

### **1.4 Kubernetes (K8s)**

- **简介**：Kubernetes 是一个开源容器编排平台，用于自动化容器化应用的部署、扩展、管理等。适用于大规模集群管理。
- **特点**：
  - 容器编排：支持多主机容器调度、负载均衡、自动扩展等功能。
  - 强大的自动化：支持自动化的部署、滚动更新和容器自愈。
  - 高扩展性：适合跨多个节点管理容器，支持混合云部署。

| 特性           | **Docker**                                       | **Podman**                                 | **Containerd**                                        | **Kubernetes**                                              |
| -------------- | ------------------------------------------------ | ------------------------------------------ | ----------------------------------------------------- | ----------------------------------------------------------- |
| **用途**       | 容器引擎，包含镜像管理、容器生命周期管理、编排等 | 容器引擎，强调无守护进程和 rootless 容器   | 容器运行时，专注于容器的创建和管理                    | 容器编排平台，管理大规模容器集群                            |
| **架构**       | 基于守护进程（Docker Daemon）                    | 无守护进程，每个命令为独立进程             | 高性能容器运行时，不提供完整的容器构建和编排功能      | 分布式系统，负责集群管理与自动化容器编排                    |
| **安全性**     | 容器默认以 root 权限运行                         | 支持 rootless 容器，增强安全性             | 无需 root 权限，可在容器运行时实现安全控制            | 通过 RBAC 和安全策略提供集群级别的安全性管理                |
| **镜像构建**   | 提供完整的镜像构建、推送、拉取功能               | 不支持镜像构建，依赖外部工具（如 Buildah） | 仅支持容器运行，不处理镜像构建功能                    | 无内建镜像构建功能，依赖外部工具（如 Docker 或 Containerd） |
| **多容器管理** | 支持多容器管理，通过 Docker Compose 实现         | 支持通过 Pod 概念实现多容器管理            | 支持多容器管理，但需要外部工具如 Kubernetes 或 Docker | 支持多容器管理，通过 Pod 和服务进行调度与负载均衡           |
| **支持平台**   | 主要用于单机或小规模部署                         | 适用于单机、开发环境和轻量级的生产部署     | 适用于 Kubernetes 和其他容器平台的容器运行时          | 适用于大规模集群，尤其是需要容器编排的生产环境              |

#### Docker和Podman都是容器引擎，但是裸跑容器还存在很多问题，

- 部署容器纯靠手动run、大规模部署麻烦
- 没有有效的容灾、自愈机制、恢复麻烦
- 扩容缩容麻烦
- 没有统一的配置管理工具

#### 什么情况下适合k8s

- 应用是微服务架构的，或者应用数量多，
- 开发需要快速部署新功能到测试环境中验证的，可以利用名称空间技术实现资源隔离
- 容器自动化管理、可以实现故障监控与自愈、自动化部署、根据并发压力进行一个扩容与缩容
- 降低硬件成本、提高资源利用率

#### 什么情况下不适合k8s

- 对于没有超高并发需求或较简单应用的场景，Kubernetes 可能过于复杂。
- 如果部署环境比较简单或规模较小，Kubernetes 的复杂性可能带来过度设计。

#### k8s的三大技术优点

- **Namespace 名称空间**：容器中用名称空间技术来隔离：`pid`、`ipc`、`uts`、`mount`、网络、`user`。将彼此资源隔离开，互不影响
- **Pod:**Pod 内可以组织一个或多个容器，Pod 内的多个容器共享网络，Pod 内的多个容器共享该 Pod 的存储卷。
- **Cgroup：**容器的技术，具有资源限制的功能，可以将容器进行资源限制、例如限制容器使用的 CPU 和内存。这样可以避免某个容器过度使用资源，影响其他容器的运行。

#### 最终方案

- 镜像构建：使用dockerfile来构建镜像，确保应用的环境一致性，并通过版本控制管理镜像的构建，示例如下：

  ```dockerfile
  ROM alpine:3.18
  COPY app /opt/app
  EXPOSE 8080
  CMD ["/opt/app/start.sh"]
  ```

- CI/CD持续集成发布（如果需要）：可以实现构建、测试、发布、和部署的自动化、减少人工干预，大致流程如下图

  ![](C:\Users\<USER>\Downloads\deepseek_mermaid_20250718_416d72.png)

- 自动化部署：通过k8s实现容器的自动化部署、扩展和管理，

**优点：**

- 自动化与回滚：CI/CD的集成可以实现自动化、提升效率，部署失败，k8s可以回滚到上一个稳定版本的状态
- 高可用与扩展：k8s可以自动管理容器的调度、负载均衡、扩容等。确保服务的高可用性，k8s还可以确保应用由多个副本在不同节点上运行。
- 一致性：dockerfile构建镜像可以确保环境的一致性、使用相同的镜像与资源，减少环境差异导致的问题
- 跨平台：k8s可以在不同环境下实现统一的管理，
- k8s可以集成集群的全链路监控与故障分析，prometheus+grafana+zipkin+exporter+EFK,实现全链路的故障与日志分析平台

**缺点：**

- k8s、CI/CD工具链技术相对复杂，数据库等有状态服务在k8s中非常依赖PV/PVC
- k8s本身会消耗一定的资源，集群规模较小或资源较小，可能会浪费一些计算资源，k8s的管理组件也会占用一定的CPU和内存
- k8s的版本更新频繁，新版本与旧版本兼容性会有问题，
- k8s生态系统中由很多外部组件（Helm，Prometheus、Istio等），配置起来也相对复杂
- k8s是通过调度器来进行资源分配和调度的，在高性能计算场景下，可能会遇到资源调度不合理的问题，高效利用物理资源是一个挑战问题
- k8s的网络与传统的虚拟化环境有所不同，涉及到网络策略、Ingress、Service、Pod网络之间的通信。

