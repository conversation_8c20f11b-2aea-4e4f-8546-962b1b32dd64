### 2025/7/30日报

### 一、今日完成工作内容

#### 1. **MySQL 容器配置与迁移完善**

- 调整 MySQL 容器启动方式，**加载自定义配置文件** `my_custom.cnf`，实现以下功能：
  - 开启 **通用查询日志**
  - 开启 **错误日志**
  - 开启 **慢查询日志**
- 挂载相关日志路径到宿主机指定目录，实现日志持久化存储和外部访问。
- 整理并完成 **MySQL 容器完整迁移文档**，涵盖内容如下：
  - 自定义配置文件设计与加载方法
  - 容器中数据库备份与恢复流程（包含备份压缩、导入等）
  - 数据库账号及权限恢复方法
  - 数据存储持久化策略（包括挂载路径说明）

#### 2. **Odoo 镜像优化与构建**

- 对现有 Odoo 镜像进行了**精简与优化**，重构 Dockerfile：
  - 合并依赖安装步骤，缩小镜像体积
  - 统一设置环境变量，简化部署参数
- 构建了一个更完整的自定义 Odoo 镜像，具备以下特性：
  - 包含全部必要依赖（Python、Node.js、wkhtmltopdf 等）
  - 内嵌当前生产环境 Odoo 源码目录及配置文件
  - 预加载所有自定义模块，镜像启动即为部署状态

#### 3. **后续工作准备**

- **资源监控与资源限制**：
  - 计划在服务迁移完成后，使用 Prometheus + Grafana 持续观察 Odoo 和 MySQL 的容器资源消耗（CPU、内存、I/O 等）
  - 制定初步资源限制策略，避免资源占满影响其他服务
- **备份与日志脚本容器化**：
  - 计划将现有 MySQL 备份脚本、登录日志提取脚本**改为可在容器中执行的版本**
  - 支持定时任务执行（后续考虑结合 `cron` 容器或 `host cron + docker exec` 实现）

------

### 二、存在问题 / 风险点

- MySQL 容器的日志文件增长需控制，计划引入 logrotate 或分割机制。

------

### 三、明日工作计划

- 编写适用于容器的 MySQL 自动备份脚本与日志提取脚本，并测试其可用性。
- 开始编写 Prometheus 配置文件，用于集成 MySQL 和 Odoo 容器的资源监控指标。
- 尝试将现有服务迁移到 Kubernetes 集群环境中，验证服务正常性与数据一致性。
- 预研 MySQL 及 Odoo 容器的资源限制参数配置与实际效果。