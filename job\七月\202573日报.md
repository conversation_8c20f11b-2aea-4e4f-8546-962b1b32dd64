#### 2025/7/3日报

**今日工作总结：**

1. **系统卡死问题排查与修复**
   - 针对近期系统频繁卡死的问题进行了深入排查。
   - 重点检查了系统日志（`dmesg`、`syslog`、`journalctl`）、内存、CPU占用、磁盘I/O等指标；
   - 排查了可能引发卡死的服务、定时任务、网络连接等问题；
   - 初步修复部分潜在问题，系统运行恢复正常，后续将继续监控稳定性。
2. **FRP + 公网穿透方案实现**
   - 完成 FRP 穿透方案的部署与调试，实现软路由公网访问功能；
   - 成功穿透 3 个端口服务：
     - **iStoreOS 管理界面**
     - **文献下载 Web 界面**
     - **NAS 管理界面**
   - 已实现通过公网访问以上 3 个服务，无需内网 VPN；
   - 当前访问正常，后续持续观察连通性与稳定性。

**后续计划：**

- 持续观察系统运行情况，收集更多日志，定位潜在问题根因；
- 监测 FRP 公网穿透的稳定性与安全性，必要时优化配置；
- 规划后续的访问权限与安全加固措施。