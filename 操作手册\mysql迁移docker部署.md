### mysql迁移docker部署

#### 一、导出数据库

```bash
mysqldump -u root -p --databases MR-TADF_DATA > /tmp/MR-TADF_DATA.sql
```

#### 二、上传到新机器

```bash
scp  MR-TADF_DATA.sql root@ip:/tmp/
```

#### 2.1、自定义配置文件

```bash
mkdir -p /opt/mysql/data /opt/mysql/conf.d /opt/mysql/logs
vim  /opt/mysql/conf.d/my_custom.cnf

[mysqld]
log_error=/var/lib/mysql_logs/mysql80.err

general_log=ON
general_log_file=/var/lib/mysql_logs/general.log

slow_query_log=ON
slow_query_log_file=/var/lib/mysql_logs/slow.log

bind-address = 0.0.0.0
```

#### 三、启动mysql容器

```bash
docker run -d \
  --name mysql80 \
  -p 3306:3306 \
  -v /opt/mysql/data:/var/lib/mysql \
  -v /opt/mysql/conf.d:/etc/mysql/conf.d:ro \
  -v /opt/mysql/logs:/var/lib/mysql_logs \
  -e MYSQL_ROOT_PASSWORD=Admin@12345 \
  mysql:8.0.43
```

**docker-compose启动**

```yaml
#docker-compose.yml
version: "3.9"

networks:
  mysql-net:
    driver: bridge

services:
  mysql80:
    image: mysql:8.0.43
    container_name: mysql80
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: Admin@12345
    volumes:
      - /opt/mysql/data:/var/lib/mysql
      - /opt/mysql/conf.d:/etc/mysql/conf.d:ro
      - /opt/mysql/logs:/var/lib/mysql_logs
    ports:
      - "3306:3306"
    networks:
      - mysql-net
```

#### 四、导入数据库和权限

```bash
docker cp MR-TADF_DATA.sql mysql80:/MR-TADF_DATA.sql
docker exec -it mysql80 bash -c "mysql -uroot -pAdmin@12345 < /MR-TADF_DATA.sql"
```

**授权**

```bash
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
```

**4.1、权限相关sql语句**

**将下面保存为mysql_users_privileges.sql**

```sql
-- ----------------------------
-- User: analyst@%
-- ----------------------------
CREATE USER IF NOT EXISTS 'analyst'@'%' IDENTIFIED BY 'StrongP@ssw0ed!';
GRANT USAGE ON *.* TO 'analyst'@'%';
GRANT SELECT, INSERT, UPDATE, CREATE, DELETE, ALTER, CREATE TEMPORARY TABLES, CREATE VIEW, SHOW VIEW 
    ON `MR-TADF-DATA`.* TO 'analyst'@'%';

-- ----------------------------
-- User: testuser@%
-- ----------------------------
CREATE USER IF NOT EXISTS 'testuser'@'%' IDENTIFIED BY 'NewBh@888888';
GRANT USAGE ON *.* TO 'testuser'@'%';
GRANT SELECT, INSERT ON `MR-TADF-DATA`.* TO 'testuser'@'%';

-- ----------------------------
-- User: analysis@%
-- ----------------------------
CREATE USER IF NOT EXISTS 'analysis'@'%' IDENTIFIED BY 'StrongP@ssw0ed!';
GRANT USAGE ON *.* TO 'analysis'@'%';
GRANT SELECT, INSERT, UPDATE, CREATE, DELETE, ALTER, CREATE TEMPORARY TABLES, CREATE VIEW, SHOW VIEW 
    ON `medicinal_molecule_db`.* TO 'analysis'@'%';

-- ----------------------------
-- User: analysis@host
-- ----------------------------
CREATE USER IF NOT EXISTS 'analysis'@'host' IDENTIFIED BY 'StrongP@ssw0ed!';
GRANT CREATE ON *.* TO 'analysis'@'host';

-- ----------------------------
-- User: testuser@127.0.0.1
-- ----------------------------
CREATE USER IF NOT EXISTS 'testuser'@'127.0.0.1' IDENTIFIED BY 'NewBh@888888';
GRANT USAGE ON *.* TO 'testuser'@'127.0.0.1';
GRANT ALL PRIVILEGES ON `oled`.* TO 'testuser'@'127.0.0.1';

-- ----------------------------
-- User: testuser@localhost
-- ----------------------------
CREATE USER IF NOT EXISTS 'testuser'@'localhost' IDENTIFIED BY 'NewBh@888888';
GRANT USAGE ON *.* TO 'testuser'@'localhost';
GRANT ALL PRIVILEGES ON `oled`.* TO 'testuser'@'localhost';

-- ----------------------------
-- User: root@%
-- ----------------------------
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'A8d!eP@#9xLq2025';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, RELOAD, SHUTDOWN, PROCESS, FILE, REFERENCES, 
    INDEX, ALTER, SHOW DATABASES, SUPER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, 
    REPLICATION SLAVE, REPLICATION CLIENT, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, 
    CREATE USER, EVENT, TRIGGER, CREATE TABLESPACE, CREATE ROLE, DROP ROLE 
    ON *.* TO 'root'@'%' WITH GRANT OPTION;
GRANT APPLICATION_PASSWORD_ADMIN, AUDIT_ABORT_EXEMPT, AUDIT_ADMIN, AUTHENTICATION_POLICY_ADMIN, 
    BACKUP_ADMIN, BINLOG_ADMIN, BINLOG_ENCRYPTION_ADMIN, CLONE_ADMIN, CONNECTION_ADMIN, 
    ENCRYPTION_KEY_ADMIN, FIREWALL_EXEMPT, FLUSH_OPTIMIZER_COSTS, FLUSH_STATUS, FLUSH_TABLES, 
    FLUSH_USER_RESOURCES, GROUP_REPLICATION_ADMIN, GROUP_REPLICATION_STREAM, 
    INNODB_REDO_LOG_ARCHIVE, INNODB_REDO_LOG_ENABLE, PASSWORDLESS_USER_ADMIN, 
    PERSIST_RO_VARIABLES_ADMIN, REPLICATION_APPLIER, REPLICATION_SLAVE_ADMIN, RESOURCE_GROUP_ADMIN, 
    RESOURCE_GROUP_USER, ROLE_ADMIN, SENSITIVE_VARIABLES_OBSERVER, SERVICE_CONNECTION_ADMIN, 
    SESSION_VARIABLES_ADMIN, SET_USER_ID, SHOW_ROUTINE, SYSTEM_USER, SYSTEM_VARIABLES_ADMIN, 
    TABLE_ENCRYPTION_ADMIN, TELEMETRY_LOG_ADMIN, XA_RECOVER_ADMIN 
    ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- ----------------------------
-- User: root@localhost
-- ----------------------------
-- 容器中 root@localhost 已由 -e MYSQL_ROOT_PASSWORD 创建，密码为 Admin@12345
-- 为确保权限完整，可以补充授权（如需）：
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;

FLUSH PRIVILEGES;
```

**保存并拷贝到容器**

```bash
docker cp /opt/mysql/mysql_users_privileges.sql mysql80:/mysql_users_privileges.sql
docker exec -i mysql80 mysql -u root -pAdmin@12345 < /mysql_users_privileges.sql
```

#### 五、进入容器验证

```bash
docker exec -it mysql80 mysql -uroot -p
SHOW DATABASES;
SELECT user, host FROM mysql.user;
SHOW GRANTS FOR 'analyst'@'%';
SHOW GRANTS FOR 'testuser'@'%';
SHOW GRANTS FOR 'root'@'%';
SHOW GRANTS FOR 'analysis'@'%';
```

#### 六、限制cpu与内存（可选）

```bash
docker update mysql80 --cpus=0.5 --memory=512m --memory-swap=512m
```

#### 先进行观察容器资源使用状态

```bash
docker stats   #然后进行资源限制
```

#### 七、备份方案

**方案一、直接备份持久化目录**

```
opt/mysql/data       #持久化目录，存放mysql所有数据，不包括日志与配置
```

**方案二、备份指定库**

```
#备份指定库，使用备份脚本
```

```bash
#backup_all.sh     备份所有库
#!/bin/bash

# 配置参数
BACKUP_DIR="/opt/mysql/backups"
LOG_DIR="/opt/mysql/logs"
CONTAINER_NAME="mysql80"
DB_USER="root"
DB_PASSWORD="Admin@12345"
DATE=$(date +"%Y%m%d%H%M%S")
RETENTION_DAYS=30
BACKUP_FILE="${BACKUP_DIR}/mysql_backup_all_${DATE}.sql"
BACKUP_FILE_COMPRESSED="${BACKUP_FILE}.gz"

# 设置数据库密码
export MYSQL_PWD=$DB_PASSWORD

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 备份所有数据库
docker exec ${CONTAINER_NAME} mysqldump -u ${DB_USER} --all-databases --single-transaction --routines --triggers --events > ${BACKUP_FILE}

# 压缩备份文件
gzip ${BACKUP_FILE}

# 删除超过 RETENTION_DAYS 的备份文件和日志
find ${BACKUP_DIR} -type f -name "mysql_backup_*.sql.gz" -mtime +${RETENTION_DAYS} -exec rm -f {} \;
find ${LOG_DIR} -type f -name "*.log" -mtime +${RETENTION_DAYS} -exec rm -f {} \;

echo "MySQL all-databases backup completed: ${BACKUP_FILE_COMPRESSED}"

```

```bash
#backup.sh   指定库备份
#!/bin/bash

# 配置参数
BACKUP_DIR="/opt/mysql/backups"
LOG_DIR="/opt/mysql/logs"
CONTAINER_NAME="mysql80"
DB_USER="root"
DB_PASSWORD="Admin@12345"
DATE=$(date +"%Y%m%d%H%M%S")
RETENTION_DAYS=30

# 指定要备份的数据库
DB_NAME="MR-TADF_DATA"   # <- 这里直接设置数据库名

BACKUP_FILE="${BACKUP_DIR}/mysql_backup_${DB_NAME}_${DATE}.sql"
BACKUP_FILE_COMPRESSED="${BACKUP_FILE}.gz"

# 设置数据库密码
export MYSQL_PWD=$DB_PASSWORD

# 创建备份目录（如果不存在）
mkdir -p ${BACKUP_DIR}

# 执行备份
docker exec ${CONTAINER_NAME} mysqldump -u ${DB_USER} --databases ${DB_NAME} --single-transaction --routines --triggers --events > ${BACKUP_FILE}

# 压缩备份文件
gzip ${BACKUP_FILE}

# 删除超过 RETENTION_DAYS 的备份文件和日志
find ${BACKUP_DIR} -type f -name "mysql_backup_*.sql.gz" -mtime +${RETENTION_DAYS} -exec rm -f {} \;
find ${LOG_DIR} -type f -name "*.log" -mtime +${RETENTION_DAYS} -exec rm -f {} \;

echo "MySQL backup for database '${DB_NAME}' completed: ${BACKUP_FILE_COMPRESSED}"
```

**创建计划任务**

```bash
crontab -e
0 3 * * * /path/to/backup.sh    #每天凌晨三点备份
```

```bash
#添加权限
chmod +x /opt/mysql/backup.sh
```

