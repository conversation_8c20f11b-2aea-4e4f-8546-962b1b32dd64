#### 2025/6/27日报
 **今日完成工作内容总结：**

1. **文献下载自动化完善**
   - 实现通过**文献名解析对应的期刊下载链接**，结合学校网络和已有期刊权限，成功避免依赖 Sci-Hub 等公共渠道；
   - 编写并优化了 Python 脚本，完成文献名到下载链接的解析逻辑；
   - 编写并完善 Shell 脚本，支持批量处理 `.txt` 文件中的文献名，实现：
     - 成功文献备份；
     - 失败文献记录日志并保留；
     - 自动触发 Aria2 下载任务；
     - 文件处理流程更加稳健、自动化；
   - 确保执行过程中，**失败任务不会误删源文件**，增强错误容忍度与后续排查能力。
2. **服务器监控工具部署**
   - 为排查近期服务器卡死问题，成功安装了 **Netdata** 简易监控面板：
     - 实时观测 CPU、内存、磁盘、网络等系统资源状态；
     - 支持图形化展示，方便远程浏览及历史趋势分析；
   - 验证 Netdata 随系统启动运行，且支持基本中文显示；
   - 后续计划基于 Netdata 监控日志进一步分析系统瓶颈或硬件异常。

**存在问题 / 待优化：**

- Aria2 下载部分文献可能因链接失效或权限问题失败，计划后续增加重试机制；
- Netdata 当前使用默认配置，尚未设置告警或邮件通知，计划进一步增强告警联动能力。

