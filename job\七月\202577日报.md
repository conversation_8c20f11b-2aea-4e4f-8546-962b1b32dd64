### 2025/7/7 日报

#### 今日工作内容

1. **软路由及存储恢复**

- 处理软路由和存储设备因过热自动关机的问题，完成设备重启和恢复。
- 成功恢复下载任务状态，保障下载不中断。

1. **文献下载代码优化**

- 优化了文献下载脚本，重点解决了 PDF 解析频繁失败的问题，提高下载成功率。
- 持续观察优化效果，确保代码稳定性。

1. **下载进度监控系统开发**

- 设计并实现基于 Web 端的 aria2 下载进度展示界面。
- 实现多台软路由的下载进度实时汇总，端口 5000 对外展示，便于集中监控。

1. **网络状态监控**

- 持续监控 frp 穿透及公网网络状态，保障远程访问的稳定性。
- 进行相关日志及流量分析，预防潜在网络风险。

1. **数据库运维**

- 定期检查数据库备份完整性，确保数据安全。
- 监控数据库日志，及时发现和处理异常。

1. **系统状态监控平台部署**

- 成功部署 Prometheus + Node Exporter + Grafana 监控平台，用于监控系统各项指标（CPU、内存、磁盘、网络等）。
- 配置基础监控面板，直观展示软路由及服务器运行状态，辅助后续运维工作。

1. **系统卡死问题排查与网卡驱动优化**

- 针对近期服务器频繁出现 CPU 飙升和系统卡死的问题，分析发现主要原因是网卡中断过于频繁，导致 CPU 负载异常、系统卡顿。
- 原因包括：网卡驱动 r8169 性能不佳、未开启中断节流、软中断集中在单核处理等。
- 采用 ELRepo 源安装并切换至 r8125 专用驱动，成功替换 r8169 驱动，提升中断处理效率。
- 调整中断绑定及软中断配置，缓解 CPU 负载问题，系统运行稳定性明显提升。

------

#### 遇到的问题及解决方案

- **文献 PDF 解析失败频繁**：通过优化解析逻辑并增强异常处理机制，显著降低失败率，目前持续观察中。
- **网卡中断导致 CPU 飙升与系统卡死**：通过更换高性能网卡驱动 r8125，并优化中断分配及处理机制，有效解决系统卡死问题。

------

#### 明日工作计划

- 继续监控文献下载代码稳定性，进一步提升解析成功率。
- 优化 Web 端下载进度展示界面，增加用户体验功能。
- 深入分析 frp 穿透网络性能，探索优化方案。
- 继续数据库日志分析，完善备份策略。
- 丰富 Grafana 监控面板指标，增加报警配置，提升监控覆盖面。