| 测试维度 | 工具      | 安装命令（Ubuntu/RHEL通用）  | 测试命令                                                     | 功能说明                   |
| -------- | --------- | ---------------------------- | ------------------------------------------------------------ | -------------------------- |
| **CPU**  | sysbench  | `apt/yum install sysbench`   | `sysbench cpu --cpu-max-prime=20000 run`                     | 多核运算性能、算力测试     |
|          | stress-ng | `apt/yum install stress-ng`  | `stress-ng --cpu 4 --timeout 60s`                            | 强压CPU（指定核数）        |
|          | 7z        | `apt/yum install p7zip-full` | `7z b`                                                       | 压缩算法CPU性能基准        |
| **内存** | sysbench  | 同上                         | `sysbench memory --memory-total-size=10G run`                | 内存吞吐带宽测试           |
|          | stress-ng | 同上                         | `stress-ng --vm 2 --vm-bytes 2G --timeout 60s`               | 内存高负载压力测试         |
|          | memtester | `apt/yum install memtester`  | `memtester 4G 1`                                             | 检查内存稳定性、bit错误    |
| **磁盘** | fio       | `apt/yum install fio`        | `fio --name=test --rw=randwrite --bs=4k --size=1G --numjobs=4 --runtime=60 --group_reporting` | 随机写 IOPS、延迟、吞吐    |
|          | dd        | 已预装                       | `dd if=/dev/zero of=testfile bs=1G count=1 oflag=direct`     | 顺序写速度测试             |
|          | ioping    | `apt/yum install ioping`     | `ioping .`                                                   | 磁盘延迟测试               |
| **网络** | iperf3    | `apt/yum install iperf3`     | `iperf3 -s`（服务端），`iperf3 -c <IP> -t 60`（客户端）      | 网络带宽/吞吐/丢包         |
| **监控** | htop      | `apt/yum install htop`       | `htop`                                                       | 彩色实时资源监控           |
|          | glances   | `pip install glances`        | `glances`                                                    | 多维性能实时仪表盘         |
|          | iotop     | `apt/yum install iotop`      | `iotop`                                                      | 磁盘I/O实时监控            |
|          | dstat     | `apt/yum install dstat`      | `dstat -cdnm --top-cpu`                                      | 综合 CPU/磁盘/网络资源显示 |

### 一键压测脚本

```bash
#!/bin/bash

LOG="benchmark_results.log"
: > $LOG  # 清空旧日志

log() {
    echo -e "\n========== $1 ==========" | tee -a $LOG
}

check_install() {
    if ! command -v $1 &> /dev/null; then
        echo "安装中: $1"
        if command -v apt &> /dev/null; then
            sudo apt update && sudo apt install -y $1
        elif command -v yum &> /dev/null; then
            sudo yum install -y $1
        fi
    fi
}

echo "🚀 启动服务器压测..."

# 检查工具
for tool in sysbench stress-ng fio dd ioping iperf3 htop; do
    check_install $tool
done

# 1. CPU 测试
log "1. CPU 性能测试 (sysbench)"
sysbench cpu --cpu-max-prime=20000 run | tee -a $LOG

log "1.1 CPU 压力测试 (stress-ng)"
stress-ng --cpu 4 --timeout 30s | tee -a $LOG

# 2. 内存测试
log "2. 内存吞吐测试 (sysbench)"
sysbench memory --memory-total-size=4G run | tee -a $LOG

log "2.1 内存压力测试 (stress-ng)"
stress-ng --vm 2 --vm-bytes 1G --timeout 30s | tee -a $LOG

# 3. 磁盘 I/O 测试
log "3. 磁盘随机写 (fio)"
fio --name=randwrite --rw=randwrite --bs=4k --size=512M --numjobs=2 --runtime=30 --group_reporting | tee -a $LOG

log "3.1 磁盘顺序写 (dd)"
dd if=/dev/zero of=./testfile bs=1G count=1 oflag=direct | tee -a $LOG
rm -f testfile

log "3.2 磁盘延迟测试 (ioping)"
ioping . -c 10 | tee -a $LOG

# 4. 网络测试 (对本机)
log "4. 网络吞吐测试 (iperf3)"
iperf3 -s -D
sleep 2
iperf3 -c 127.0.0.1 -t 10 | tee -a $LOG
pkill iperf3

# 5. 总结
log "✅ 测试完成，结果输出到 $LOG"

```

