### 2025/7/10日报

**工作内容：**

1. **数据库备份检查与排查：**
   - 完成了日常数据库备份的检查，确保备份任务正常执行。
   - 排查了Linux机器卡死问题，进行了相关分析并找到了潜在的原因。
2. **文献下载平台优化：**
   - 对文献下载平台进行了优化，增强了PDF解析能力，提升了解析的准确性和成功率。
   - **Unpaywall API集成与页面爬取：** 完成了对Unpaywall API的集成，并增强了页面爬取的兜底机制。支持查找a、link、button、iframe、embed等标签中的PDF下载入口，确保了更多文献能够被抓取。
3. **Sci-Hub镜像增强：**
   - 集成了20多个最新的Sci-Hub镜像，作为最后的兜底方案，确保了即便在无法直接通过其他渠道下载时，也能通过Sci-Hub镜像进行访问和下载。
   - 自动遍历所有可用的Sci-Hub镜像，确保下载不中断。
4. **DOI格式兼容性优化：**
   - 现在支持用户输入多种格式的DOI，如标准DOI、带前缀DOI（如doi:、https://doi.org/等）、以及期刊官网URL（如science.org、wiley.com等），系统能够自动提取标准DOI，免去用户手动转换的麻烦。
5. **PDF解析测试：**
   - 测试了大部分PDF文献的解析功能，成功率大幅提升，系统能够顺利解析大部分文献。