### 2025/八月工作月报——苏国峰

**工作内容总结**

本月主要围绕系统迁移、容器化部署、文档编写、数据管理等方面开展工作，涉及多个技术平台和业务系统的运维与优化。

- 持续整理种子下载进度，并统计，实时汇报
- 对潘老师的DOI文件利用学校网络和软路由进行下载，并同步拷贝给潘老师
- 配合处理文物遗址检测系统（云冈石窟）
- 评估Thinkpad与华为擎云两款笔记本的适用性
- 重新安装两台新采购的华为笔记本，原linux，安装windows，并安装所需驱动

- 完成服务迁移各种测试，编写部署所需yaml文件，研究docker中gpu使用，测试NVIDIA Container Toolkit配置
- 完成odoo与postgresql、Mysql数据库的docker-compose文件配置
- 测试mysql数据库迁移后的账户权限问题，解决权限失效问题
- 部署EAM固定资产管理系统，包含Mysql、MinIo、Nginx、BPM等服务
- 测试Nginx容器代理功能
- 编写容器技术文档，内容包含
  - 容器VS虚拟机对比分析
  - 容器核心技术原理与Docker Runtime
  - 容器内CPU与GPU使用方式及调度机制
  - OOM机制及排查思路
  - 容器性能限制与参数配置
  - 容器进程管理、PID Namespace及进程隔离
  - 容器磁盘配额与overlay2文件系统
  - Docker基础操作
  - docker-compose多容器编排
  - 容器安全（镜像签名、SBOMOPA策略控制）
- 停止学校种子下载任务（速度过慢），重新分配给本地三台软路由进行下载，并清理其他来源的重复下载任务，避免重复下载，制作统一表格并使用颜色标记下载进度和状态
- 日常维护鸿之微登录节点，计算节点的种种问题
- 日常维护东方超算实例的种种问题，创建实例，数据备份，反馈问题等
- 对比调研三个开源固定资产管理系统，WGFIX、Snipe-it、和EAM，最终选定EAM并计划采用docker-compose部署
- 编写eam系统的docker-compose文件，并进行镜像制作等
- 编写odoo与postgresql、mysql数据库的docker-compose配置
- 配合停止云冈石窟系统
- 将学校工作站迁移至公司，重装linux系统
- 迁移odoo、eam、mysql等服务至新工作站
- 配置nginx代理及端口转发，实现公网访问
- 编写odoo与eam数据库备份脚本
- 配置数据库定期自动备份计划任务
- 到其他学校进行拷贝数据，并进行二次拷贝双份，快送至鸿之微
- 将其他方式下载的种子文件，传输到软路由
- 对EAm系统移除redis模块，优化eam架构整理目录结构，并将EAM系统镜像上传至私有仓库，方便后续部署与管理
- 协助其他工程师解决端口转发问题
- 修复Odoo的`hongzhiwei_project`模块中装饰器错误问题（@api.model改为@api.model_create_multi）
- 整理odoo与eam以及mysql在docker环境下的备份方案，编写备份脚本
- 根据需求启动并维护东方超算的服务器
- 排查超算服务器故障问题并解决
- 配合重新启动文物遗址（云冈石窟）系统
- 维护超算与鸿之微服务器的各种问题并解决
- 对EAM系统jar包的本地构建，处理私有仓库依赖问题
- 日常对系统的备份计划任务进行观察，观察所有容器的资源使用情况，对日志进行检查。