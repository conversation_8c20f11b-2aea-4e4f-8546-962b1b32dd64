#### aria2监听文件下载脚本

```
#!/bin/bash

# 监控目录，放种子和下载链接文件
WATCH_DIR="/mnt/usb2_2-0/Public/Downloads/file"

# Aria2 JSON-RPC 
ARIA2_RPC="http://127.0.0.1:6800/jsonrpc"

log() {
    echo "[$(date '+%F %T')] $*"
}

add_torrent() {
    local torrent_file="$1"
    local base64_data
    base64_data=$(base64 -w0 "$torrent_file")
    
    local json=$(cat <<EOF
{
  "jsonrpc":"2.0",
  "method":"aria2.addTorrent",
  "id":"$(date +%s%N)",
  "params":[
    "$base64_data",
    [],
    {}
  ]
}
EOF
)
    local resp
    resp=$(curl -s -H "Content-Type: application/json" --data-binary "$json" "$ARIA2_RPC")

    if echo "$resp" | grep -q '"result"'; then
        log "✅ 成功添加种子：$(basename "$torrent_file")"
        rm -f "$torrent_file"
    else
        log "❌ 添加失败种子：$(basename "$torrent_file")，响应：$resp"
    fi
}

add_uri() {
    local url="$1"
    local json=$(cat <<EOF
{
  "jsonrpc":"2.0",
  "method":"aria2.addUri",
  "id":"$(date +%s%N)",
  "params":[
    ["$url"]
  ]
}
EOF
)
    local resp
    resp=$(curl -s -H "Content-Type: application/json" --data-binary "$json" "$ARIA2_RPC")

    if echo "$resp" | grep -q '"result"'; then
        log "✅ 成功添加链接：$url"
    else
        log "❌ 添加失败链接：$url，响应：$resp"
    fi
}

# 主流程：扫描目录，处理文件
for file in "$WATCH_DIR"/*; do
    [ -f "$file" ] || continue

    case "$file" in
        *.torrent)
            log "导入种子文件：$file"
            add_torrent "$file"
            ;;
        *.txt)
            log "导入链接文件：$file"
            while IFS= read -r url || [ -n "$url" ]; do
                [[ -z "$url" ]] && continue
                add_uri "$url"
            done < "$file"
            rm -f "$file"
            ;;
        *)
            log "跳过非支持文件：$file"
            ;;
    esac
done

log "所有任务处理完毕"

```





**解释**

```
脚本存为aria2_watch.sh
给执行权限：chmod +x aria2_auto_add.sh
把 .torrent 文件和链接 .txt 文件放入 $WATCH_DIR 目录
运行脚本：bash aria2_watch.sh
任务会自动添加到 Aria2，可以在 AriaNg 界面查看下载进度
添加成功后，源 .torrent 和 .txt 文件会被自动删除，避免重复添加
```

