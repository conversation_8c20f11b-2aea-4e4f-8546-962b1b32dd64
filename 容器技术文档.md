[TOC]

# 容器技术文档

## 一、容器VS虚拟机

### 虚拟机技术

虚拟机（Virtual Machine）是一台计算机可以运行多个虚拟机，每个虚拟机运行独立的操作系统。

**精髓：**

1. **隔离**：虚拟机之间相互隔离
2. **资源限制**：虚拟机对资源的使用不会干扰其他虚拟机

**常见的虚拟化技术：**

1. KVM
2. ESXi
3. Xen
4. Hyper-V
5. VirtualBox

**问题：**

- 虚拟机过于臃肿，不够轻量
- 虚拟机本身会占用大量资源（每个VM需要完整的OS）
- 启动速度慢（需要启动完整OS）
- 资源利用率低

### 容器技术

容器是一个只包含rootfs的技术，共享主机内核。

**精髓：**

1. **隔离**：通过namespace实现资源隔离
2. **资源限制**：通过cgroup限制容器对资源的使用

**容器引擎：**
容器引擎创建一个容器，本质就是创建了一个名称空间，名称空间的核心目的就是为了隔离关键资源，保证不与其他容器冲突。

## 二、容器核心技术

### **Namespace（名称空间）**

**作用：** 实现资源的隔离，确保不同容器之间的资源不会冲突

**隔离的资源类型：**

1. **PID**：进程ID，确保不同容器内的进程ID独立
2. **UTS**：主机名与网络信息服务
3. **IPC**：进程间通信，确保容器内的进程通信独立
4. **Mount**：挂载点，容器内的文件系统与宿主机隔离
5. **Network**：网络资源（IP地址、网卡、路由等），一个名称空间里可以有自己独立的网卡，监听的端口不会与其他名称空间冲突
6. **User**：用户ID和组ID，确保不同容器内的用户ID独立
7. **Cgroup**（Linux 4.6+）：控制组命名空间，提供cgroup视图隔离
8. **Time**（Linux 5.6+）：系统时间命名空间，允许容器有自己的系统时间

### **Cgroup（控制组）**

**作用：** 限制容器对资源的使用，防止容器占用过多资源影响其他容器

**资源类型：**

1. **CPU**
2. **内存**
3. **磁盘I/O**
4. **网络带宽**
5. **设备访问**
6. **进程数限制**

**Cgroup v2新特性：**

- 统一层次结构
- 增强的资源分配策略
- 改进的内存控制器
- 支持递归资源限制

### **容器镜像**

完整系统镜像：bootfs（内核、启动文件）+rootfs（文件系统）

容器镜像：仅包含rootfs，共享主机内核

**优势：**

- 轻量级（通常只有几十MB到几百MB）
- 启动速度快（秒级启动）
- 资源占用少（共享内核）
- 易于版本控制和分发

### **UnionFS联合文件系统**

**作用：** 将多个文件系统层合并为一个统一的文件系统

**常见实现：**

- overlay2（推荐，性能最好）
- aufs
- btrfs
- zfs
- devicemapper

**overlay2结构：**

1. **lowerdir**：镜像层，只读（可以有多个）
2. **upperdir**：容器层，可写，修改的内容都会放到这一层指定的目录里
3. **merged**：展现层，合并lowerdir和upperdir的内容，容器内看到的就是这一层
4. **workdir**：工作目录，用于原子性操作

**容器的优势：**

- 轻量级：容器只包含应用程序及其依赖，不包含完整的操作系统
- 高效性：容器启动速度快，资源占用少
- 可移植性：容器可以在不同的环境中无缝迁移
- 隔离性：通过namespace和cgroup实现资源限制与隔离
- 跨平台性：容器可以在不同的操作系统和云平台上运行
- 标准化：遵循OCI（Open Container Initiative）标准
- 可扩展性：支持微服务架构
- 版本控制：镜像支持分层和版本管理

**PS:** 容器内挂载的文件系统类型不是ext4、xfs等，而是overlay文件系统，容器A修改自身的upperdir不会影响其他容器，容器镜像使用 overlayfs（如 overlay2）进行分层挂载，合并只读镜像层与可写 upperdir，容器内看到的挂载类型是 overlay，但底层依赖宿主机的 ext4/xfs 等文件系统提供存储。

## 三、Docker

Docker是一种轻量级容器化技术，通过容器实现虚拟化功能，Docker是用来创建容器、管理容器的平台。

#### **Docker组件：**

1. Docker CLI：客户端命令行工具
2. Docker Daemon：守护进程
3. Docker Registry：镜像仓库
4. Docker Objects：镜像、容器、网络、卷等

### Docker安装与配置

安装docker：

```
# Ubuntu
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io

# CentOS
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install docker-ce docker-ce-cli containerd.io

# Windows/macOS
下载Docker Desktop安装包并安装
```

### **配置docker**

**docker的配置文件在`/etc/docker/daemon.json`**

```json
{
  "exec-opts": ["native.cgroupdriver=systemd"],   # 使用systemd管理
  "registry-mirrors": [
    "https://reg-mirror.qiniu.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ], # 镜像加速，可以设置多条
  "data-root": "/opt/mydocker",   # docker数据目录
  "log-driver": "json-file",      # 日志驱动
  "log-opts": {
    "max-size": "100m",           # 单个日志文件最大100MB
    "max-file": "3"               # 最多保留3个日志文件
  },
  "storage-driver": "overlay2",   # 存储驱动
  "live-restore": true,           # 守护进程崩溃时保持容器运行
  "default-ulimits": {            # 默认ulimit设置
    "nofile": {
      "Name": "nofile",
      "Hard": 64000,
      "Soft": 64000
    }
  }
}
```

**数据目录内容：**

1. 本地的镜像
2. 容器新增的数据（没有关联存储卷）
3. 网络配置
4. 插件数据
5. 卷数据

**迁移数据目录大致步骤**

- 停止容器和服务

  ```bash
  docker stop test
  systemctl stop docker
  ```

- 挂载新磁盘并迁移数据

  ```bash
  mount /dev/sdb  /data
  cp -ra /var/lib/docker /data/docker
  ```

- 修改配置文件指向新目录

  ```bash
  "data-root": "/data/docker"
  ```

- 重启服务

  ```bash
  systemctl restart docker
  ```

### docker常用命令

**镜像操作**

```bash
docker pull nginx:latest              # 拉取镜像
docker push myimage:v1                # 推送镜像
docker save nginx -o nginx.tar        # 导出镜像
docker load -i nginx.tar              # 导入镜像
docker image ls                       # 查看镜像列表
docker image rm nginx                 # 删除镜像
docker image inspect nginx            # 查看镜像详情
docker build -t myimage:v1 .          # 构建镜像
docker image prune                    # 清理无用镜像
```

**容器操作**

```bash
docker run -d --name web nginx        # 启动容器
docker exec -it web sh               # 进入容器
docker start/stop/restart web        # 启停容器
docker rm -f web                     # 强制删除容器
docker ps -a                         # 查看所有容器
docker logs -f web                   # 查看容器日志
docker stats web                     # 查看容器资源使用
docker update --memory 512M web      # 更新容器配置
docker cp file.txt web:/path/        # 宿主机和容器间拷贝文件
```

**数据卷挂载**

```bash
docker run -v /宿主机目录:/容器目录     # 目录挂载
docker volume create myvol           # 创建卷
docker run -v myvol:/容器目录         # 卷挂载
docker volume ls                    # 列出所有卷
docker volume inspect myvol         # 查看卷详情
docker volume prune                 # 清理无用卷
```

**网络操作**

```bash
docker network ls                    # 列出网络
docker network create mynet          # 创建网络
docker network inspect bridge        # 查看网络详情
docker network connect mynet web     # 连接容器到网络
docker network disconnect mynet web  # 从网络断开容器
```

**进入容器环境**

```bash
docker exec -it 容器名/容器id  sh
```

```bash
nsenter -t 895049 -m -u -i -n -p -- 命令
```

**容器的启动停止**

```bash
docker start test
docker stop test
docker restart test
docker pause test     # 暂停容器
docker unpause test   # 恢复容器
```

**docker清理**

```bash
docker system prune      #清理已停止的容器、未使用的网络和悬空镜像
docker system prune -a   #清理所有未使用的资源
docker volume prune      #清理数据卷
docker system prune -a --volumes  #清理所有未被使用的数据卷
docker container prune   #清理已停止的容器
docker network prune     #清理未使用的网络
```

### Dockerfile与docker网络

#### **制作镜像的两种方式**

1. **Docker commit**
   - 通过docker commit命令将容器的当前状态保存为镜像
   - 不推荐在生产环境使用，因为不可重复且难以维护

2. **编写Dockerfile**
   - 通过编写Dockerfile文件，基于该文件构建镜像
   - Dockerfile是一个文本文件，包含一系列指令，用于自动化构建Docker镜像
   - 推荐方式，可重复、可版本控制、可自动化

**示例Dockerfile**

```dockerfile
# 使用 centos:7 镜像作为基础
FROM centos:7

# 设置元数据
LABEL maintainer="<EMAIL>"
LABEL version="1.0"
LABEL description="Nginx web server"

# 安装Nginx服务器
RUN yum install -y epel-release && \
    yum install -y nginx && \
    yum clean all && \
    rm -rf /var/cache/yum

# 创建网站目录
RUN mkdir -p /var/www/html && \
    chown -R nginx:nginx /var/www/html && \
    chmod -R 755 /var/www

# 复制本地网页文件到容器
COPY index.html /var/www/html/
COPY nginx.conf /etc/nginx/nginx.conf

# 设置环境变量
ENV NGINX_PORT=80
ENV NGINX_HOST=localhost

# 暴露80端口（HTTP）
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost/ || exit 1

# 设置工作目录
WORKDIR /var/www/html

# 指定容器启动时运行的用户
USER nginx

# 启动Nginx服务（前台模式）
CMD ["nginx", "-g", "daemon off;"]
```

- **Dockerfile指令详解：**
  1. **FROM**：指定基础镜像
     - 必须是第一条指令
     - 推荐使用官方镜像
     - 指定具体版本而非latest

  2. **LABEL**：添加元数据
     - 用于记录作者、版本、描述等信息

  3. **RUN**：执行命令
     - 每一条RUN指令都会创建一个新的镜像层
     - 多条命令建议用&&连接，减少层数
     - 清理不必要的缓存和临时文件

  4. **COPY**：复制文件
     - 从构建上下文复制文件到镜像中
     - 比ADD更推荐使用，因为更透明

  5. **ADD**：高级复制
     - 可以解压tar文件
     - 可以从URL下载文件
     - 除非需要特殊功能，否则推荐使用COPY

  6. **ENV**：设置环境变量
     - 会被持久化到容器中
     - 可以被后续指令和容器运行时使用

  7. **EXPOSE**：声明端口
     - 只是文档作用，实际不会发布端口
     - 需要用-p参数发布端口

  8. **WORKDIR**：设置工作目录
     - 为后续指令设置工作目录
     - 如果目录不存在会自动创建

  9. **USER**：指定运行用户
     - 改变后续指令和容器运行时的用户
     - 推荐使用非root用户

  10. **VOLUME**：定义匿名卷
      - 创建挂载点
      - 防止数据写入容器可写层

  11. **CMD**：容器启动命令
      - 每个Dockerfile只能有一条CMD指令
      - 可以被docker run的参数覆盖
      - 三种形式：
        - CMD ["executable","param1","param2"] (exec形式，推荐)
        - CMD command param1 param2 (shell形式)
        - CMD ["param1","param2"] (作为ENTRYPOINT的参数)

  12. **ENTRYPOINT**：入口点
      - 配置容器启动时的执行命令
      - 不会被忽略，一定会被执行
      - 两种形式：
        - ENTRYPOINT ["executable", "param1", "param2"] (exec形式，推荐)
        - ENTRYPOINT command param1 param2 (shell形式)

  13. **ARG**：构建时变量
      - 只在构建时有效
      - 可以用--build-arg覆盖

  14. **HEALTHCHECK**：健康检查
      - 定义容器健康检查方式
      - 可以检测应用是否真的可用

PS：CMD命令设置容器启动后默认执行的命令及参数，但是CMD命令能够被docker run命令后的命令行参数替换

​	ENTRYPOINT配置容器启动时的执行命令（不会被忽略，一定会被执行）

​	**每个dockerfile文件只有一条CMD命令，如果指定多条命令，只有最后一条会被执行**
**多阶段构建示例**

```dockerfile
# 第一阶段：构建应用
FROM golang:1.16 AS builder
WORKDIR /app
COPY . .
RUN go build -o myapp .

# 第二阶段：运行应用
FROM alpine:latest  
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/myapp .
CMD ["./myapp"]
```

### Docker网络

- 单机网络：
  - 在同一台宿主机上的多个容器的网络组织和通信方式
- 四种方式
  - Bridge：默认模式，容器通过docker0网桥通信
  - Host：容器直接使用宿主机的网络
  - None：容器没有网络
  - Container：与某个容器共享网络

**四种网络模式的原理与特点**

- Bridge模式

  - 原理：

    - Docker默认的网络模式，使用docker0网桥作为虚拟交换机
    - 每个容器会创建一个ceth对，一端连接容器内的eth0，一段连接docker0网桥
    - 容器的网关指向docker0的IP地址

  - 特点：

    - 同一台宿主机上的多个容器通过docker0网桥进行二层通信
    - 容器访问外网时，流量通过docker0网桥转发到宿主机的网卡
    - 外网访问容器时，需要通过端口映射将宿主机的端口映射到容器的端口

    ps：查看虚拟机交换机docker0网桥接口情况

    ```
    brctl show
    brctl showmacs docker0
    ```

    **确定veth对的配对情况**

    1.进入容器内，查看容器网卡配对的veth设备的id号

    ```
    cat /sys/class/net/eth0/iflink
    ```

    2.退出，执行ip link show

- Host模式

  - 原理：
    - 容器直接使用宿主机的网络命名空间，共享宿主机的网络栈
    - 容器不会创建自己的网络接口，而是直接使用宿主机的网络接口
  - 特点:
    - 容器内的端口直接绑定到宿主机的端口，无需端口映射
    - 容器可以直接访问宿主机网络，适合需要高性能网络的应用
    - 容器与宿主机共享网络，安全性较低

- None模式：

  - 原理：
    - 容器拥有自己的网咯命名空间，但不进行任何网络配置
    - 容器没有网络接口，无法与外界通信
  - 特点：
    - 容器完全隔离，没有网络连接
    - 适合需要完全隔离网络环境的场景，如安全性要求极高的应用

- Container模式

  - 原理：
    - 容器与另一个容器共享网络命名空间
    - 两个容器共享同一个网络栈
  - 特点：
    - 共享网络的容器可以通过lo回环接口直接通信
    - 适合需要紧密协作的容器，如日志收集容器与应用容器

**启动时指定网络**

```bash
docker run -ti --name "test1" --network=bridge centos:7 /bin/bash
 
docker run -ti --name "test2" --network=host centos:7 /bin/bash
 
docker run -ti --name "test3" --network=none centos:7 /bin/bash
 
# 关于container网络
docker run -d --name "aaa" --network=bridge centos:7 sleep 1000
 
docker run -d --name "bbb" --network=container:aaa centos:7 sleep 1000
```

**跨主机网络**

- 不同宿主机上的容器如果想要通信
- 两种技术
  - **Macvlan**
  
    - 为容器分配MAC地址，使其直接连接到物理网络
  
    - 容器就像物理机一样出现在网络中
  
    - 需要网络设备支持
  
    - 配置示例：
  
      ```bash
      # 创建Macvlan网络
      docker network create -d macvlan \
        --subnet=***********/24 \
        --gateway=*********** \
        -o parent=eth0 \
        macvlan_net
      ```
  - **Overlay**
  
    - 基于VXLAN技术，实现跨主机的容器通信
  
    - Docker Swarm模式下自动配置
  
    - 需要键值存储（如Consul、Etcd）进行协调
  
    - 配置示例：
  
      ```bash
      # 初始化Swarm
      docker swarm init
      # 创建Overlay网络
      docker network create -d overlay my_overlay
      ```
  
  **其他跨主机方案：**
  
  - Weave Net
  - Flannel
  - Calico
  - Cilium

**自定义网络**

Docker允许创建自定义网络，提供更好的隔离和控制

```bash
# 创建自定义bridge网络
docker network create --driver bridge \
  --subnet 172.28.0.0/16 \
  --gateway 172.28.5.1 \
  --opt com.docker.network.bridge.name=mybridge \
  my_custom_network

# 使用自定义网络
docker run -d --name web --network my_custom_network nginx
```

**网络连接与断开**

```
# 连接容器到网络
docker network connect my_custom_network web

# 从网络断开容器
docker network disconnect my_custom_network web
```

## 四、Docker Compose

Docker Compose是一个用于定义和运行多容器Docker应用程序的工具。通过YAML文件配置应用服务，然后使用单个命令创建和启动所有服务。

### 安装Docker Compose

```bash
# Linux
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# macOS/Windows
Docker Desktop已包含Compose，无需单独安装
```

### Compose文件结构

**示例docker-compose.yml：**

```yaml
version: '3.8'

services:
  web:
    image: nginx:alpine
    container_name: web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./html:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf
    networks:
      - frontend
    depends_on:
      - db
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:13
    container_name: db
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: appdb
    volumes:
      - db_data:/var/lib/postgresql/data
    networks:
      - backend
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  redis:
    image: redis:alpine
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - backend
    command: redis-server --requirepass secret

networks:
  frontend:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16
  backend:
    driver: bridge

volumes:
  db_data:
    driver: local
```

### Compose配置详解

1. **version**：指定Compose文件格式版本
2. **services**：定义各个服务
   - **image**：使用的镜像
   - **build**：从Dockerfile构建
   - **ports**：端口映射
   - **volumes**：卷挂载
   - **networks**：连接的网络
   - **environment**：环境变量
   - **depends_on**：服务依赖
   - **restart**：重启策略
   - **healthcheck**：健康检查
   - **deploy**：部署配置（资源限制等）
3. **networks**：自定义网络配置
4. **volumes**：自定义卷配置

### Compose常用命令

```bash
docker-compose up -d        # 启动服务（后台模式）
docker-compose down         # 停止并移除所有容器
docker-compose ps           # 查看服务状态
docker-compose logs -f      # 查看日志
docker-compose build       # 构建或重新构建服务
docker-compose pull        # 拉取服务镜像
docker-compose config      # 验证和查看配置
docker-compose exec web sh # 进入web服务容器
docker-compose pause       # 暂停服务
docker-compose unpause     # 恢复服务
docker-compose restart    # 重启服务
docker-compose top        # 显示运行中的进程
```

### Compose高级特性

1. **扩展字段**：使用`x-`前缀定义自定义字段
2. **多文件配置**：使用`-f`指定多个配置文件
3. **环境变量替换**：在Compose文件中使用`${VAR}`语法
4. **配置文件继承**：使用`extends`字段
5. **服务编排**：控制启动顺序（`depends_on` + `healthcheck`）
6. **资源限制**：通过`deploy.resources`设置
7. **秘密管理**：使用`secrets`配置敏感数据

## 五、Runtime

- OCI：全称OPen Container Initiative（开放容器倡议），指在指定容器技术的通用技术标准
- **目前有两种标准：**
  1. **容器运行时标准**（runtime-spec）
  2. **容器镜像标准**（image-spec）

### Docker架构演进

**Docker 1.11+架构：**

Docker由单体架构分为了五大组成部分：

1. **docker-client**：客户端命令
2. **dockerd守护进程**（docker daemon）：
   - 提供客户端命令接口
   - 镜像构建、存储卷管理、日志操作等
3. **containerd服务**：
   - 独立负责容器运行时的生命周期
   - 创建、启动、停止、暂停、信号处理、删除
4. **containerd-shim**：
   - 每创建一个容器，都会启动一个containerd-shim进程
   - 调用runc来具体创建容器
   - 管理容器生命周期
5. **runc**（容器运行时）：
   - 符合OCI标准的标准容器运行时
   - 负责创建符合OCI规范的容器

**为什么需要containerd-shim进程？**

- runc创建的容器需要依赖于某个进程或服务
- 如果直接依赖于containerd服务，那么一台机器上的所有容器都会依赖于containerd
- 一旦containerd退出，所有容器都会退出
- 引入containerd-shim进程后：
  - 由该进程调用runc来创建容器
  - 容器创建出来后依赖于containerd-shim进程，而不是containerd服务
  - containerd-shim进程的父进程是1号进程
  - containerd服务挂掉后，不会影响containerd-shim进程

### **containerd**、**containerd-shim**及容器进程关系

**层级关系：**

```
dockerd服务 --> containerd服务 --> 多个containerd-shim进程 --> runc --> 容器的1号进程
```

**依赖关系：**

- **dockerd服务**：没有依赖，父进程是1号进程
- **containerd服务**：没有依赖，父进程是1号进程
- **containerd-shim**：
  - 由containerd创建，但父进程是1号进程
  - 创建后与containerd无关
- **容器内的1号进程**：
  - 依赖于containerd-shim
  - 父进程是containerd-shim
  - 容器结束后进入僵尸状态，由containerd-shim负责回收
  - 容器的标准输入、文件描述符等依赖于containerd-shim管理

### **Runtime**

- Runtime是容器运行时，用于管理镜像或容器的服务端软件

- Runtime分为两大类：
  - High-level Runtime：如Docker containerd、podman等，支持更多高级功能，搞级别运行时通过调用低级别运行来管理容器，通常使用runc作为低级别运行时
  - Low-level Runtime：如LXC、runc、gVisor、Kata等，只涉及容器运行的基础细节，如Namespace创建、Cgroup设置等

## 六、容器内CPU与GPU

- CPU：擅长逻辑控制和串行运算，能够处理各种复杂的计算任务
- GPU：擅长大规模的并发计算，能够同时处理大量简单的计算任务

### **在容器内使用GPU**

在容器内使用GPU需要具备以下条件：

- 宿主机必须要有至少一块GPU卡
- 宿主机上需要为该GPU卡安装驱动程序
- 安装官方的容器引擎，例如Docker
- 配套安装一个**`nvidia-container-runtime`**
- 启动容器时使用参数--gpus指定使用的GPU，或者修改配置文件，将默认的runc替换为nvidia-container-runtime，这样容器启动时就不需要加--gpus参数，容器默认都能访问GPU

**正常创建容器的流程**

```bash
docker--->dockerd--->containerd--->containerd-shim--->runc---container-process
```

**创建GPU容器的流程**

```bash
docker--->dockerd--->containerd--->containerd-shim--->nvidia-container-runtime--->nvidia-container-runtimr-hook--->libnvidia-container--->container-process
```

**使用方式**

```bash
docker run --gpus all nvidia/cuda:11.0-base nvidia-smi
docker run --gpus 2 nvidia/cuda:11.0-base nvidia-smi
docker run --gpus '"device=0,1"' nvidia/cuda:11.0-base nvidia-smi
```

**或者修改配置文件**将默认的runc替换为nvidia-container-runtime：

```json
{
  "default-runtime": "nvidia",
  "runtimes": {
    "nvidia": {
      "path": "/usr/bin/nvidia-container-runtime",
      "runtimeArgs": []
    }
  }
}
```

**PS：容器环境内必须有CUDA环境才能正常使用GPU**

## 七、容器的关键进程

### **容器内的进程组织或关系**

**0号进程：containerd-shim进程**

- 角色：containerd-shim进程是容器操作系统的祖宗进程，如果它挂掉，整个容器也会退出
- 特点：
  - 创建容器：containerd-shim调用runc来创建容器，容器内执行的指令都由该shim进程接收处理，如果shim结束，容器也会随之挂掉
  - 父进程角色：containerd-shim是容器的父进程，一旦容器内的1号进程结束，整个容器名称空间里的进程都会被0号进程回收
  - 僵尸进程处理：如果1号进程没有结束，但其子进程结束，子进程会成为僵尸进程，由1号进程回收，如果1号进程没有回收僵尸进程的能力，僵尸进程会累积
  - 信号处理：docker stop停止容器时，会向容器的1号进程发送-15信号（平滑关闭），如果1号进程没有信号转发能力，她会向容器名称空间中的所有进程发送-9信号（强制杀死），如果1号进程由信号转发功能，它会向所有进程发送-15信号
- 总结：`containerd-shim` 是运行在宿主机上的一个代理进程，负责保持容器运行、处理容器内 PID 1 的退出回收以及日志转发。容器内的 PID 1 是容器自身的第一个进程（通常是应用进程或 init 进程），与宿主机的 PID 命名空间隔离。

**1号进程：容器内启动的第一个进程**

- 角色：1号进程是容器内的第一个进程，代表了整个容器的生命周期，如果1号进程结束，容器也会结束
- 特点：
  - 生命周期：1号进程的结束意味着容器的结束，0号进程结束，容器也会结束
  - 前台运行：容器内的1号进程必须是一个在前台一直运行的进程
- 与操作系统功能的区别：
  - 非所有进程的祖先：容器内的1号进程并不一定是所有用户进程的祖先
  - 孤儿进程处理：如果1号进程成为孤儿，他会成为其子进程的父进程
  - 回收与信号转发：我们自己开发的1号进程可能没有回收僵尸进程和转发信号的能力
- 1号进程应具备的能力：
  - 回收僵尸进程：1号进程应具备回收僵尸子进程的能力
  - 信号转发：1号进程应具备转发信号的能力

### **对比完整的linux操作系统**

**0号进程**：负责创建宿主机的操作系统，是宿主机操作系统的祖宗进程，如果他挂掉，整个宿主机操作系统也会挂掉

**1号进程**：

- 角色：操作系统的第一个进程，代表了整个操作系统的生命周期，操作系统也会结束
- 特点：
  - 守护进程：1号进程以守护进程的形式存在
  - 四大功能：
    - 管理操作系统内的应用程序进程，即1号进程是应用程序进程的祖先
    - 收养孤儿进程
    - 会定期发起wait或waitpid的功能去回收成为僵尸的儿子
    - 将操作系统信号转发给子进程

**2号进程**：负责创建内核相关进程

**查看容器进程**

- 查看容器PID号：

  ```bash
  docker inspect test | grep -i pid
  ps -elf | grep 上一个命令查出来的pid
  ps aux | grep 上一个命令过滤出的pid
  ```

- 查看容器所有正在运行的进程（不显示僵尸进程和孤儿进程）

  ```bash
  docker top test
  ```

## 八、容器信号及平滑关闭容器

### **进程收到信号后的三种反应**

- 忽略（Ignore）：进程对该信号不做任何处理
- 捕获（Catch）：进程可以注册自己的信号处理函数（handler），当信号到来时执行该函数
- 缺省（Default）：Linux系统为每个信号定义了默认行为，进程会按照默认行为处理信号

**两个特权信号**

- SIGKILL（-9）：强制杀死进程
- SIGSTOP（-19）：暂停进程的运行

**特权信号的特点：**

- 无法被忽略
- 无法被捕获

**其他信号（如-15）**

- -15信号：进程可以选择忽略或捕获该信号

**在容器内执行信号操作**

- kill - 9 1：无法杀死容器内的1号进程
- kill -19 1：无法暂停容器内的1号进程
- kill -15 1：有可能杀死1号进程

ps：在容器内直接对 PID 1 发送 `SIGKILL` 信号会导致容器内该进程终止，容器退出。但在宿主机上对容器内 PID 1 发信号，需要考虑 namespace 映射和权限，某些 runtime 会有信号代理机制，可能导致无法直接生效。

#### 九、Cgroup

**Cgroup是一种用于限制容器对宿主机资源使用量的机制**

CPU的状态信息

```
us  User：用户态进程占用CPU时间的百分比，不包括低优先级进程的用户态时间（nice值1-19）。
sys System：内核态进程占用CPU时间的百分比。
ni  Nice：nice值1-19的进程用户态占CPU时间的百分比，注意ni与us一样，都是在用户态。
id  Idle：系统空闲CPU的百分比。
wa  Iowait：系统等待I/O的CPU时间占比，该时间不计入进程的CPU时间。
hi  Hardware irq：处理硬件中断的总时间/CPU的总时间，该时间不计入进程的CPU时间。
si  Softtirq：处理软件中断的时间，该时间不计入进程的CPU时间。
st  Steal：表示同一宿主机上的其他虚拟机抢走的CPU时间。
```

**PS：**

- 中断处理：无论是硬件中断（hi）还是软件中断（si），占用的CPU时间都不会计入进程的CPU时间，中断程序是独立的，不属于用户态或内核态，因此Cgroup不会限制它们
- 不可中断睡眠（D）：代表进程正在进行的I/O操作，可以将进程的I/O操作比喻为取快递，进程是取快递的人，磁盘是快递员，进程等待磁盘完成I/O操作时，进入不可中断睡眠状态，此时进程不能被kill掉，否则会导致数据丢失
- 可中断睡眠（S）：代表进程在等待某些事件（如用户输入），进程进入可中断睡眠状态时，可以被kill掉，

**CPU Cgroup的使用**

每个Cgroup子系统通过虚拟文件系统挂载到缺省目录下，在linux发行版中，CPU Cgroup通常挂载到/sys/fs//cgroup/cpu目录下，在该目录下，每个控制组都是一个子目录，控制组之间的关系是树状层级结构

**创建控制组**

```bash
cd /sys/fs/cgroup/cpu
mkdir group2
```

创建控制组后，系统会自动在该目录下生成一系列的文件：

```bash
cgroup.clone_children  cpuacct.usage_percpu  cpu.shares
cgroup.event_control   cpu.cfs_period_us     cpu.stat
cgroup.procs           cpu.cfs_quota_us      notify_on_release
cpuacct.stat           cpu.rt_period_us      tasks
cpuacct.usage          cpu.rt_runtime_us
```

**删除控制组**

直接使用rm -rf 删除控制组目录会报错，不允许删除，可以通过libcgroup工具来删除控制组

```bash
# 安装libcgroup工具
# REDHAT系统安装
yum install libcgroup libcgroup-tools -y

# UBUNTU系统安装
apt-get install cgroup-bin

# 验证是否安装成功
cgdelete -h

# 删除控制组
cgdelete cpu:/group1
cgdelete cpu:/group2

# 如果有子group，需要使用-r参数递归删除
cgdelete -r cpu:/group1
```

CPU Cgroup中与CFS相关的参数

CFS（Completely Fair Scheduler）是Linux内核中的一种调度器，Cgroup通过以下参数来控制CPU的使用率：

1. **cpu.cfs_period_us**：代表CPU的一个时间周期，单位为微秒（μs）。例如，100ms（100,000μs）。
2. **cpu.cfs_quota_us**：代表在该时间周期内，控制组内的进程可以使用的CPU时间。例如，50ms（50,000μs）。
   - **计算公式**：`cpu.cfs_quota_us / cpu.cfs_period_us`。例如，50ms / 100ms = 0.5，表示在100ms的总周期内，该控制组内的进程最多使用0.5个CPU。
3. **cpu.shares**：用于控制同一层级下的多个控制组之间的CPU资源分配。当多个控制组的进程对CPU的占用超过宿主机的实际CPU个数时，`cpu.shares`会生效。

ps：在k8s中非常简单，由两个与CPU资源相关的参数，request对应cpu.shares，代表申请资源。limit对应cpu.cfs_quota_us/cpu.cfs_period_us，控制资源使用的上线，最用用到limit设置的值。

## 十、监控容器CPU真实使用率

在容器内无法通过top命令获取真实的CPU使用率

#### **获取单个进程对CPU的使用率**

- 关于CPU使用率：

  - 某个进程对CPU的利用率 = 用户进程占用CPU时间/CPU经历的一段时间
  - 进程对CPU利用率为100%代表使用1颗CPU
  - 进程对CPU利用率为200%代表使用2颗CPU

- 关于Load Average:

  - 在某段时间内平均活跃的进程数（包含系统处于可运行状态及不可中断状态的平均进程数）
  - 如果宿主机有4颗CPU，那么平均负载可以超过4

- 进程从启动开始，linux系统就会累计进程对CPU的占用时间

  - 负责记录进程对CPU资源累计占用的是linux系统中的/proc文件系统

  - top命令就是通过查看/proc文件系统中每个进程对应的stat文件中的两个数值来完成统计的

    ```bash
    cat /proc/进程id/stat | awk '{print $14,$15}'
    ```

- 内容有很多，主要关注第14项utime和第15项stime：

  - utime：代表进程的用户态部分在linux系统调度中获取的CPU的ticks
  - stime：代表进程的内核态部分在linux系统调度中获取到CPU的ticks

  ticks是linux系统的一个时间单位，一个tick代表一次终端的周期，这个周期耗费的时间由中断频率HZ决定，可以用命令查看，默认为100

```bash
[root@localhost ~]# getconf CLK_TCK
100
```

#### **统计一个进程的CPU利用率：**

进程对CPU的利用率 = 在这段时间内该进程占用了多久的CPU/CPU经历的一段时间

```
进程的 CPU 使用占比 = ((utime_2 – utime_1) + (stime_2 – stime_1)) / (HZ * et * 1)
 
上述结果为一个小数，想要得到百分率，需要乘以 100，如下：
进程的 CPU 使用百分率 = ((utime_2 – utime_1) + (stime_2 – stime_1)) * 100 / (HZ * et * 1)
```

#### **统计整个系统对CPU的使用率**

查看/proc/stsat

```bash
[root@localhost ~]# cat /proc/stat
cpu  224140 138 142895 80629390 23209 82333 43272 0 0 0
cpu0 7065 5 3299 3350730 843 3193 13255 0 0 0
cpu1 2361 0 1470 3375220 490 1434 1544 0 0 0
cpu2 11127 2 5660 3354052 1462 4854 1695 0 0 0
cpu3 2261 0 1185 3378064 488 860 318 0 0 0
cpu4 12204 1 6339 3350323 1622 5763 1947 0 0 0
cpu5 2304 0 1148 3378146 462 834 317 0 0 0
```

- 前八列对应的就是该CPU上，`us, sys, ni, id, wa, hi, si, st` 累积的 ticks 数。

#### **获取容器对CPU的真实占用率**

**启动一个容器：**

```bash
docker run -d --name test centos-7 sh -c "i=0,while true;do let i++;done"
```

**获取容器id:**

```bash
 docker inspect --format="{{.Id}}" test
```

**进入宿主机的容器目录**

```bash
cd /sys/fs/cgroup/cpu
cd system.slice/docker-56aa4b20acff0d017b311d2aa8396f71af0f035bb1a94fbf541b5467d21a2359   #替换为查出的容器id
```

**查看cpuacct.stat**

```bash
user 66842   # 这个就是当前控制组里所有进程的用户态ticks
system 2550  # 这个就是当前控制组里所有进程的内核态ticks
```

**整个容器对CPU的占用率可以用公式计算**

```
进程的 CPU 使用率 = ((utime_2 – utime_1) + (stime_2 – stime_1)) * 100.0 / (HZ * et * 1)
```

**示例**

```bash
cat cpuacct.stat;sleep 3;cat cpuacct.stat
user 120231
system 4605
user 120521
system 4615
```

```
((120521 - 120231) + (4615 - 4605)) * 100 / (100 * 3 * 1)
```

**Docker容器监控**

命令：

```bash
docker stats 容器名/id
```

`docker stats` 统计结果只能是当前宿主机的容器，并且数据是实时的。为了更全面的监控，可以使用 `cadvisor`。

cAdvisor可以对节点机器上的资源及容器进行实时监控和性能数据采集，包括CPU使用情况、内存使用情况、网络、文件等

安装并运行cAdvisor

```bash
docker pull google/cadvisor:latest
docker run \
  --volume=/:/rootfs:ro \
  --volume=/var/run:/var/run:rw \
  --volume=/sys:/sys:ro \
  --volume=/opt/mydocker:/var/lib/docker:ro \
  --volume=/dev/disk/:/dev/disk:ro \
  --publish=8080:8080 \
  --detach=true \
  --name=cadvisor \
  google/cadvisor:latest
```

访问http://ip:8080/containers/即可观察到监控数据

访问http://ip:8080/docker查看容器

**ps：通常情况下配合prometheus采集+grafana出图可以清晰观察**

## 十一、OOM机制

OOM是linux系统的一种保护机制

当一台机器上运行的进程对内存的占用达到一定量时，就会触发OOM机制，然后杀死某个正在运行的进程（OOM机制会对进程进程打分）

**触发OOM的情况分为两种：**

- 达到物理机的最大内存使用量：此时一定会触发OOM机制，系统会从全局角度杀死进程所有进程都是目标
- 达到容器的内存限制：即使物理机内存充足，但某个容器内的进程对内存的使用量达到了cgroup的限制，也会触发OOM机制，此时只能杀掉该控制组内的进程

**系统级OOM发生时，应该杀掉哪个进程**

当系统级OOM发生时，系统不会随时杀进程，而是通过内核oom_badness()函数来评定，评定的主要依据有两个：

- 进程已经使用的物理内存页面数
- 每个进程的OOM校准值oom_score_adj

在/proc系统中，每个进程都有一个`/proc/[pid]/oom_score_adj` 文件，可以在该文件中输入 -1000 到 1000 之间的任意数值，来调整该进程被OOM杀死的几率。`oom_badness()` 函数会计算一个评分：

```
评分 = 系统总的可用页面数 * oom_score_adj值 + 进程已经使用的物理页面数
```

评分越高，被OOM杀死的几率越大。

**memory cgroup的使用**

每创建一个容器，都会创建一个对应的memory cgroup控制组，路径通常为

```bash
/sys/fs/cgroup/memory/system.slice/docker-xxxx
```

主要关注以下三个参数：

- **`memory.limit_in_bytes`**：控制容器内所有进程可以占用的物理内存上限。

  - 如果上级（父级）group 设置的 `memory.limit_in_bytes` 为 500M，那么子 group 最多只能设置 500M，超过 500M 的设置将失效。

- **`memory.oom_control`**：默认为 0，代表开启OOM机制；设置为 1 代表关闭OOM机制。

  ```
  echo 1 > memory.oom_control
  ```

- **`memory.usage_in_bytes`**：这是一个只读参数，表示容器内所有进程占用的物理内存总量。当控制组内所有进程占用内存达到上限时，会触发OOM

**分析OOM日志**

OOM日志中由三个重要内容：

- 容器进程的mem_alloc：可以看到占用的RSS（物理内存的页数，默认每页4kb）和oom_score_adj。
  - 如果列出的是整个操作系统的进程，可以断定发生了一次系统级的OOM。
- 发生OOM的容器或控制组：可以确定是那个容器发生了OOM
- 被OOM杀死的进程的PID：可以看到最后被OOM杀死的进程的PID

根据这些信息，处理方案有两种：

- **调大 `memory.limit_in_bytes`**：如果被OOM杀死的进程本身需要大量内存，可以适当调大内存限制。
- **修复内存泄漏**：如果进程存在BUG导致内存泄漏，达到了 `memory.limit_in_bytes` 的限制，需要修复BUG。

**容器内关于内存的使用**

`memory.usage_in_bytes`（容器内所有进程对内存的使用量） = 进程实际占用的物理内存大小（RSS） + page cache 的大小（可释放）。

**注意：要看容器内所有进程占用的实际内存，`memory.usage_in_bytes` 并不准确，查看 RSS 才是最准确的，可以通过 `memory.stat` 查看。**

- **page cache**：是 Linux 系统用来提升内存利用率的机制，当内存不足时，系统会释放 page cache 来给 RSS 使用。

手动释放page cache:

```
echo 3 > /proc/sys/vm/drop_caches
```

**Swap分区**

swap分区的本质时磁盘空间，用来扩展内存

swap对系统级的影响

- 启用了swap分区的物理机，可用内存 = 物理内存大小+swap分区大小，当物理分区不足时，系统会开始使用swap分区（虽然会降低系统运行效率，但是可以避免系统崩溃）

- 系统级的OOM会在可用内存不足时触发，如果宿主机启用了swap分区，系统OOM会在物理内存和swap分区都用完的情况下触发

- 系统级OOM出发时，每个进程的评分计算公式为：

  ```
  评分 = 系统当前剩余可用内存 * oom_score_adj + 当前进程占用的物理内存
  ```

**swap分区对容器级OOM的影响**

- 如果swap分区开启，即使容器内所有进程占用的内存量达到了容器的最大内存限制，也不会触发OOM，系统会开始使用swap分区

**限制容器对swap的使用**

```bash
docker run -m 200M --memory-swap=300M
# --memory-swap=物理内存 + swap大小
```

##### 控制全局与局部的swap使用

**需求**：宿主机上一些容器需要使用 swap 分区，另一些容器不需要使用。

1. **启动 swap 分区**。

2. 设置系统的 `swappiness` 参数，控制 swap 分区与内存释放的比重。

   ```bash
   /proc/sys/vm/swappiness
   ```

3. 针对个别容器关闭 swap 分区的使用，在创建容器时添加参数：

   ```bash
   docker run -it --memory-swappiness=0 ubuntu:16.04 /bin/bash
   ```

**补充**：容器的 `memory.swappiness` 与全局的 `swappiness` 有两点不同：

1. 容器自己的优先级更高。
2. 如果把 `memory.swappiness` 设置为 0，对于容器来说就是彻底关闭了对 swap 分区的使用；而把系统级的 `swappiness` 设置为 0，则代表尽量不用，并非彻底不用。

**可压缩资源与不可压缩资源**

1. **CPU 是可压缩资源**：
   - 进程对 CPU 的使用率超过了限制，不会被干掉，但会限制进程对 CPU 的最大使用量。
2. **内存、磁盘是不可压缩资源**：
   - 进程对内存的占用率达到限制时，会触发 OOM 机制，将其干掉。
   - 内存一旦被占用，如果不释放，就会一直占用；而 CPU 的时间片是共享的，操作系统为了实现并发会随时夺走进程的 CPU 执行权限。

**K8s 中关于容器资源的限制**

##### CPU 的限制（CPU cgroup）

1. **`request`**：控制的是 `cpu.shares`。
2. **`limit`**：控制的是 `cpu.cfs_quota_us/cpu.cfs_period_us` 的比值。

##### 内存的限制（memory cgroup）

1. **`request`**：不配置任何 memory cgroup 相关的参数，`request` 本身只是一个创建容器时的调度指标，并不修改任何参数。
2. **`limit`**：控制的是 `memory.limit_in_bytes`。

## 十二、脏数据对容器读写性能的影响

### **用户进程写文件的完成流程**

1. 用户进程调用 `f.write()`。
2. 系统调用 `sys_write()`。
3. 虚拟文件系统（VFS）处理请求。
4. 文件系统层（如 ext4、xfs）处理请求。
5. **Page Cache** 缓存数据。
6. Block I/O 层处理请求。
7. 设备驱动层处理请求。
8. 数据最终写入硬件设备（如硬盘、USB）。

**ps:在文件系统层（ext4、xfs）和Block I/O层之间，存在Page Cache层**

### 文件的 I/O 模式

文件的 I/O 模式分为两种：

1. **Buffer I/O**：
   - 数据先写入缓冲区（buffer），然后由内核异步将脏数据写入磁盘。
   - 优点：读写效率高，适用于大多数场景。
   - 缺点：
     - 数据写入缓冲区后，若在等待内核线程将数据写入磁盘的过程中发生断电或崩溃，可能导致数据丢失。
     - 可能出现缓存被写爆的情况。
     - 一次性写入过多数据可能导致系统卡顿，因为系统可能会从异步写入切换到同步写入。
2. **Direct I/O**：
   - 数据直接写入磁盘，不经过缓冲区。

**什么是脏数据？**

在 **Buffer I/O** 模式下，数据先写入 **Page Cache**，若还未及时写入磁盘，这部分数据称为 **脏数据**（Dirty Pages）。

#### 脏数据的落盘机制

在 Linux 内核中，有专门的内核线程（如 `kworker/flush` 线程）负责定期将脏数据写入磁盘。

#### Buffer I/O 的优缺点

- **优点**：读写效率高，适用于大多数场景。
- 缺点：
  - 读缓存通常有益无害。
  - 写缓存较为复杂，可能导致数据丢失、缓存写爆或系统卡顿。

**查看脏数据相关参数**

可以用通过以下命令查看与脏数据相关的内核参数

```bash
sysctl -a | grep dirty
```

输出示例解释：

```bash
vm.dirty_background_bytes = 0  # 配置路径：/proc/sys/vm/dirty_background_bytes
vm.dirty_background_ratio = 10 # 配置路径：/proc/sys/vm/dirty_background_ratio
 
vm.dirty_bytes = 0             # 配置路径：/proc/sys/vm/dirty_bytes
vm.dirty_ratio = 20            # 配置路径：/proc/sys/vm/dirty_ratio
 
vm.dirty_writeback_centisecs = 500 # 配置路径： /proc/sys/vm/dirty_writeback_centisecs
vm.dirty_expire_centisecs = 3000   # 配置路径： /proc/sys/vm/dirty_expire_centisecs 
 
vm.dirtytime_expire_seconds = 43200 # 配置路径： /proc/sys/vm/dirtytime_expire_seconds
```

**与脏数据落盘相关的重要内核参数**

- **vm.dirty_background_ratio**：
  - 表示内存中可以填充脏数据的百分比。
  - 例如，设置为 10，表示允许 10% 的内存用于存放脏数据，超过该比例时，后台进程会开始清理脏数据。
- **vm.dirty_ratio**：
  - 表示系统内存中可以填充脏数据的绝对最大比例。
  - 当脏数据量达到此比例时，所有新的 Buffer I/O 操作都会被阻塞，直到脏数据写入磁盘。
  - 这是导致长 I/O 卡顿的原因之一，但也是防止内存中脏数据过多的保护机制。
- **vm.dirty_background_bytes** 和 **vm.dirty_bytes**：
  - 这两个参数是 `vm.dirty_background_ratio` 和 `vm.dirty_ratio` 的另一种表示方式。
  - 如果设置了 `bytes` 版本，则 `ratio` 版本将变为 0，反之亦然。
- **vm.dirty_writeback_centisecs**：
  - 指定 `pdflush/flush/kdmflush` 进程唤醒的时间间隔（单位为百分之一秒）。
  - 默认值为 500，表示每 5 秒唤醒一次，检查是否有缓存需要清理。
- **vm.dirty_expire_centisecs**：
  - 指定脏数据在内存中的存活时间（单位为百分之一秒）。
  - 默认值为 3000，表示脏数据在内存中停留超过 30 秒后，会被异步写入磁盘。
- **vm.dirtytime_expire_seconds**：
  - 默认值为 43200 秒（12 小时）。
  - 如果一个 inode 在 12 小时内没有更新，系统会强制检查其时间戳，避免文件时间戳更新过于迟滞。

**脏数据的占用比例**

脏数据的占用比例 **A** 计算公式：

```
A = 脏数据占用的内存页面数 / 当前可用内存的页面数
```

- **vm.dirty_background_ratio = 10**：当 A 超过该比例时，系统会开始清理脏数据。
- **vm.dirty_ratio = 30**：当 A 达到该比例时，所有 Buffer I/O 写操作都会被阻塞，直到脏数据写入磁盘。
- **vm.dirty_expire_centisecs = 3000**：脏数据在内存中停留超过 30 秒后，会被写入磁盘。
- **vm.dirty_writeback_centisecs = 500**：每 5 秒唤醒一次内核的 flush 线程来处理脏数据。

**查看内存中的脏数据**

可以通过以下命令查看内存中的脏数据：

```bash
[root@localhost ~]# cat /proc/vmstat | egrep "dirty|writeback"
```

输出示例：

```bash
nr_dirty 0
nr_writeback 0
nr_writeback_temp 0
nr_dirty_threshold 102963
nr_dirty_background_threshold 34321
```

**脏数据内核优化参数的使用场景**

1. **减少缓存，加大落盘**：

   ```bash
   # cat /etc/sysctl.conf
   vm.dirty_background_ratio = 5 # 减少
   vm.dirty_ratio = 10 # 减少
   ```

   执行 `sysctl -p` 命令使配置生效。

2. **增加缓存**：

   ```bash
   vm.dirty_background_ratio = 50 # 增加
   vm.dirty_ratio = 80 # 增加
   ```

3. **增减都用**：

   ```bash
   vm.dirty_background_ratio = 5 # 减少
   vm.dirty_ratio = 80 # 增加
   ```

**有用的 proc 文件**

以下 proc 文件提供了大量有用的系统信息：

```bash
/proc/vmstat
/proc/meminfo
/proc/iostat
/proc/sys/vm
```

通过这些文件，可以深入了解系统的内存、I/O 和脏数据的状态。

## 十三、限制容器磁盘的IO性能

### **限制容器对磁盘的IO性能的必要性**

在一台宿主机上运行的多个容器共享宿主机的资源，包括磁盘IO。因此，容器的IO速率会相互影响。为了避免这种影响，应该为每个容器设置合理的IO限制。

此外，容器内的文件系统（通常是OverlayFS）不适合频繁的写操作。因为容器内发起的写操作会经过OverlayFS，最终转换为操作系统的文件系统（如ext4、xfs或网络文件系统）的写操作。这种转换会增加额外的开销。因此，如果容器内涉及频繁的写操作，建议为容器挂载单独的Volume。

### **基于cgroup1限制容器磁盘I/O**

在cgroup v1中，blkio子系统可以用来限制磁盘的IO,blkio cgroup的虚拟文件系统通常挂载在`/sys/fs/cgroup/blkio/`目录下。以下是四个重要的参数：

- 读与写的IOPS（每秒输入/输出操作数）：
  - `blkio.throttle.read_iops_device`
  - `blkio.throttle.write_iops_device`
- 读与写的带宽（每秒字节数）：
  - `blkio.throttle.read_bps_device`
  - `blkio.throttle.write_bps_device`

示例用法：

为容器设置磁盘IO限制的示例

```bash
# 为test1容器设置
CONTAINER_NAME="test3"
CONTAINER_ID=$(docker ps --format "{{.ID}}\t{{.Names}}" | grep -i $CONTAINER_NAME | awk '{print $1}')
echo $CONTAINER_ID 
CGROUP_CONTAINER_PATH=$(find /sys/fs/cgroup/blkio/ -name "*$CONTAINER_ID*") 
echo $CGROUP_CONTAINER_PATH

# 获取/tmp/test1目录挂载源的设备的主、次设备号
[root@test04 ~]# ls -l /dev/sda
brw-rw---- 1 root disk 8, 0 8月  17 12:22 /dev/sda

# 注意：我们要查看的是设备的主次设备号，而不是某一个分区的。
# 如上所示，/dev/sda的主次设备号分别为8和0。

# 配置容器test1对设备/dev/sda的读写带宽均为10M/s
# 后续测试数据库都固定为4k，所以此处限制磁盘性能只需要配置带宽即可
echo "8:0 10485760" > $CGROUP_CONTAINER_PATH/blkio.throttle.read_bps_device
echo "8:0 10485760" > $CGROUP_CONTAINER_PATH/blkio.throttle.write_bps_device
```

**解释：**

- **CONTAINER_NAME**：指定要限制IO的容器名称。
- **CONTAINER_ID**：通过`docker ps`命令获取容器的ID。
- **CGROUP_CONTAINER_PATH**：找到该容器对应的cgroup路径。
- **设备的主次设备号**：通过`ls -l /dev/sda`命令获取设备的主次设备号。
- **限制带宽**：通过`echo`命令将限制写入cgroup的`blkio.throttle.read_bps_device`和`blkio.throttle.write_bps_device`文件，限制容器的读写带宽为10MB/s。

## 十四、容器磁盘配额

- 默认情况下，容器内的可用磁盘空间是没有限制的
- 容器内的文件系统由lowerdir和upperdir组成
  - lowerdir：只读层，通常是镜像的基础层
  - upperdir：可写层，容器内写入的数据会存储在这一层
- 当容器内的任何目录没有挂载外部存储卷时，写入的数据都会存储在upperdir层，也就是宿主机上
- 如果不限制容器的磁盘使用，容器可能将宿主机的磁盘空间写满，导致系统问题

**解决方法**

- 对容器的可用磁盘进行配额：限制容器可以使用的磁盘空间
- 挂在外部存储：将容器写操作的目录挂载到专门的外部存储卷上，避免占用宿主机磁盘空间

**对容器可用磁盘进行配额**

示例：单独限制某一个容器

```bash
docker run -d --name test1 --storage-opt size=100M centos:7 tail -f /dev/null
```

该命令启动一个名为test1的容器，并将其磁盘使用限制为100MB

**设置全局的默认值**

可以通过修改/etc/docker/daemon.json配置文件来设置全局的磁盘配额，以下配置将每个容器的磁盘空间限制为1GB

```json
{
  "data-root": "/data/docker",
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true",
    "overlay2.size=1G"
  ]
}
```

- `data-root`：指定 Docker 的数据存储路径。
- `storage-driver`：指定存储驱动为 `overlay2`。
- `storage-opts`：设置存储选项，`overlay2.size=1G` 表示每个容器的磁盘空间限制为 1GB。

## 十五、容器日志管理

每个容器都有自己的日志，用于接收标准输出的内容

查看日志路径

```bash
docker inspect 容器名/ID | grep -i logpath
```

- 该命令可以查看容器日志的存储路径

**控制容器日志的大小**

启动容器时，可以通过参数来控制日志的文件个数和单个文件的大小：

```bash
docker run -it --log-opt max-size=10m --log-opt max-file=3 redi
```

- `max-size`：单个日志文件的最大大小（例如 10MB）。
- `max-file`：最大日志文件数（例如 3 个文件）。

同样可以全局配置，通过修改配置文件配置日志大小和文件数

```json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "50m",
    "max-file": "3"
  }
}
```

- `max-size=50m`：单个日志文件的最大大小为 50MB。
- `max-file=3`：最多保留 3 个日志文件。如果超过这个数量，最旧的日志文件将被删除。
  - **注意**：`max-file` 仅在 `max-size` 设置时有效，默认值为 5。
- **注意**：已存在的容器不会自动应用这些配置，需要重新启动容器才能生效。

## 十六、容器安全

容器安全不仅设计镜像来源，还包括运行时隔离、权限控制和供应链安全

### 镜像扫描

- 使用漏洞扫描工具在构建部署前检查镜像：

```bash
# 安装 trivy
#ubuntu
sudo apt install trivy
#redhat
sudo yum install trivy
# 扫描镜像漏洞
trivy image nginx:latest
```

- 可以集成到CI流水线，发现漏洞时，组织构建

### 最小化镜像

- 使用精简基础镜像，

- 删除不必要的包和缓存：

  ```
  RUN apk add --no-cache curl \
   && rm -rf /var/cache/apk/*
  ```

### 非root运行

- 在Dockerfile中创建并使用非root用户：

  ```
  RUN useradd -m appuser
  USER appuser
  ```

- 在K8s中，Pod spec可设置：

  ```
  securityContext:
    runAsUser: 1000
    runAsNonRoot: true
  ```

  