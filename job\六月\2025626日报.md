#### 2025/6/26日报

**一、今日完成工作内容**

1. **搭建易有云组网环境，打通多台设备互访通道**
   - 使用易有云实现软路由与其他终端之间的内网穿透与组网连接；
   - 配置端口转发规则，调试易有云的连接状态，确保多态软路由能正常互通；
   - 验证从笔记本端、虚拟机端、局域网中多台设备通过易有云中转实现高效通信；
   - 作为跳板机的软路由部署成功，为后续远程运维、分布式节点调试打下基础。
2. **持续监控与测试 Aria2 下载服务运行状态**
   - 跟踪 Aria2 的运行日志，优化配置参数（如最大连接数、最大线程数、种子上传策略）；
   - 验证通过 RPC 方式远程调用 Aria2 接口进行种子任务添加、状态查询、日志记录；
   - 测试了种子文件的自动监控与下载脚本，确保脚本能实时扫描目录并导入下载任务；
   - 调整任务日志输出格式，方便后续集成统一监控平台。
3. **软路由作为跳板机的验证与多平台远程访问**
   - 利用软路由的 NAT 功能和组网服务，将其部署为中转跳板；
   - 成功实现外部访问软路由后继续访问内网其他主机，测试多台虚拟软路由访问一致性；
   - 探索在软路由间建立自动互通机制，尝试构建一套低成本的多点远程协同运维环境。
4. **研究文献下载自动化方案，实现文献名到下载任务的自动转换**
   - 查阅并测试了多种文献资源获取工具，包括基于搜索引擎解析的 SciHub 工具链、libgen 接口调用等；
   - 编写初步的文献名到下载链接的解析脚本，结合文献数据库名称、DOI、关键词等信息进行匹配；
   - 集成 Aria2 下载模块，实现文献名转下载链接并自动拉起下载任务的自动化流程；
   - 尝试在软路由上运行该系统，保障任务脚本具备稳定性与离线运行能力。

------

**二、问题与解决方案**

- **问题：Aria2 下载过程中个别任务长时间挂起或失败**
   **解决：**增加重试机制、延长超时配置，并手动验证相关链接有效性；引入失败重扫模块。
- **问题：软路由资源有限，任务运行偶有阻塞**
   **解决：**控制 Aria2 并发连接数，合理调度 CPU 占用，考虑通过定时调度任务避免资源冲突。

**三、后续计划**

- 整合 Aria2 的状态汇总脚本，开发 Web 前端监控界面；
- 优化文献名转下载链接的算法，增强准确性和覆盖率；
- 扩展软路由的跳板功能，测试文件同步、容器远程部署、GPU 节点调度等功能；
- 开始整理今日实验过程与脚本，计划整理为运维自动化模板或文档库资产。