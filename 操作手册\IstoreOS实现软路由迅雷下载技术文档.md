### IstoreOS实现软路由迅雷下载技术文档

一、下载istoreos镜像，下载地址

```
https://fw21.koolcenter.com:60010/iStoreOS/x86_64/istoreos-22.03.7-2025050912-x86-64-squashfs-combined.img.gz
```

pve中创建虚拟机，

常规：设置名称

操作系统：不使用任何介质（利用pve将镜像输送）

系统：非efi固件不需要改动

磁盘：直接删除，后面会挂载与添加

CPU：按需设置

内存：按需设置

网络：结合具体情况

**创建完成后先不要启动**

二、上传镜像文件到pve并解压转换格式

```
# 解压并转换镜像格式（在PVE Shell执行）
gunzip /var/lib/vz/template/iso/combined-squashfs.img.gz
qm importdisk 101 istoreos-22.03.7-2025050912-x86-64-squashfs-combined.img local-lvm --format qcow2
```

完成后在虚拟机中添加新磁盘

**调整启动顺序**

将导入的磁盘设置为第一启动项，并禁用其他选项

#### 三、配置网络（关键）

根据物理网口与pve网卡配置网卡

```
#添加两块网卡，并且设置静态ip，配置文件如下
config interface 'loopback'
	option device 'lo'
	option proto 'static'
	option ipaddr '127.0.0.1'
	option netmask '*********'

config globals 'globals'
	option ula_prefix 'fdd0:6b68:69c3::/48'

config interface 'wan'
	option device 'eth0'
	option proto 'static'
	option gateway '**************'
	option ipaddr '**************'
	option netmask '*************'
	option peerdns '0'
	list dns '*******'
	list dns '*******'

config interface 'wan6'
	option device 'eth0'
	option proto 'dhcpv6'

config device
	option name 'br-lan'
	option type 'bridge'
	list ports 'eth1'

config interface 'lan'
	option device 'br-lan'
	option proto 'static'
	option ipaddr '**************'
	option netmask '*************'
	option ip6assign '60'
	list ip6class 'wan6'
	list ip6class 'wan_6'
	list ip6class 'wan'
	option defaultroute '0'
```

**pve中配置lan口与wan口与其对应**

```
qm set 101 --net0 virtio,bridge=vmbr2
qm set 101 --net1 virtio,bridge=vmbr0
```

测试连通性

连通后直接w访问istoreos的web页面

```
http://**************
```

页面示例

![image-20250605173722613](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250605173722613.png)

进入管理页面后，配置lan口与wan口网络连通性

进入系统挂载外部存储，

![image-20250605173838275](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250605173838275.png)

进入IStore安装迅雷与Aria2

![image-20250605173914621](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250605173914621.png)

需要手动修改配置文件与下载目录

#### 成果展示

![image-20250605174010426](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250605174010426.png)

![image-20250605174022322](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250605174022322.png)

![ee6a6a93a95318f555dbb9b1cec4bba8](C:\Users\<USER>\Documents\xwechat_files\wxid_mofr8lmgi14u21_4cb7\temp\2025-06\RWTemp\9b32ba495a788fe93495c2dd2781fa1e\ee6a6a93a95318f555dbb9b1cec4bba8.png)