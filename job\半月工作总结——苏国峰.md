### 半月工作总结——苏国峰

**汇报时间：**2025/6/18

#### 一、工作内容总结

**第一周：软路由系统搭建与主机安装**

1.软路由系统配置：

​	学习并完成软路由安装配置，多种系统下选择IStoreOS系统

​	安装配置Aria2，实现离线下载功能

​	解决初期使用过程中的下载路径以及openwrt不友好的问题

2.主机硬件组装：

​	完成4太主机的装机任务，包括机箱、电源、硬盘、内存、显卡等组件组件

​	并安装操作系统，三台rockylinx9(桌面版)，一台windows(专业版)

**第二周：新增设备与组网**

1.新增软路由与存储配置

​	新增软路由与磁盘阵列存储

​	配置新增软路由及其对应存储设备

​	将存储设备配置好对应raid

​	配置软路由统一系统，挂载统一存储路径

​	保证软路由下载服务稳定运行并正确保存数据

2.异地组网搭建与测试

​	使用FeiShuVpn实现异地组网

​	并到学校进行校园网测试（发现组网可通，但是校园网会掉认证，王老师说当天校园网问题，理论可通，继续执行）

​	偶然发现组网会出现OOM内存泄漏，并导致组网掉线（第三周已解决）

**第三周：存储优化与脚本实现需求**

​	新增软路由与存储，存储须全部格式化并更改raid模式，配置为raid0，提升存储性能（无冗余）

​	对所有软路由与存储进行挂载，并且可以同时进行下载

​	实现对大量种子从后往前每100条均匀分发给不同软路由的自动分配脚本

​	并对组网掉线设置检测脚本，通过检查tun0网卡与ip来对服务进行重启

​	目前所有软路由均在执行下载任务。

**正在进行中：**

​	将数据分析的本地电脑上的mysql数据库push到linux上，并且设计将windows上的数据每天同步新的数据量到linux上，linux对外提供读写服务，需解决数据一致性和完整性，不覆盖的问题。（目前已完成全量同步的操作，同步计划采用binlog日志来进行同步，）

**接下来的计划：**

实现定时备份，异常访问监测。