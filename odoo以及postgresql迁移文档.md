### odoo以及postgresql迁移文档

一、导出数据库

```
[root@bh004 odoo]# sudo -i -u postgres
[postgres@bh004 ~]$ pg_dump -Fc odoo > /tmp/odoo_backup.dump
```

二、在目标机器创建文件夹

```
mkdir -p /opt/postgre-docker    #容器持久化目录
chmod 700 /opt/postgre-docker
mkdir -p /opt/postgre-backup    #将数据库备份文件放入这个目录
```

三、启动PostgreSQL容器

```
docker run -d \
  --name odoo-db \
  --restart unless-stopped \
  -e POSTGRES_DB=odoo \
  -e POSTGRES_USER=odoo \
  -e POSTGRES_PASSWORD=admin@123 \
  -e TZ=Asia/Shanghai \
  -v /opt/postgre-docker:/var/lib/postgresql/data \
  -v /opt/postgre-backup:/backup \
  -p 5432:5432 \
  registry.cn-shanghai.aliyuncs.com/sucloud/postgres:14
```

四、进入容器恢复备份

```
docker exec -it odoo-db bash
ls /backup/odoo_backup.dump
PGPASSWORD=admin@123 pg_restore -U odoo -d odoo /backup/odoo_backup.dump
```

五、验证数据库是否恢复

```
#1.进入容器
docker exec -it odoo-db bash
#2.切换用户
psql -U odoo -d odoo
#3.查看数据库有哪些表
\dt
```

六、迁移odoo

6.1、压缩源目录

```
cd /opt
tar czvf odoo.tar.gz odoo
```

6.2、传送到新机器

```
cd /opt
tar xzvf odoo.tar.gz
```

6.3、修改配置文件指向新的数据库

```
vim /opt/odoo/etc/odoo.conf

db_host = localhost   #改为容器名 odoo-db
db_host = odoo-db
```

6.3.1、下载所需依赖

```
https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.buster_amd64.deb
#下载完成后上传到文件夹
[root@ashersu odoo]# mkdir deps
[root@ashersu deps]# ls
wkhtmltox_0.12.6-1.buster_amd64.deb
```

6.4、创建dockerfile

```dockerfile
#cd /opt/odoo
#vim Dockerfile

FROM docker.xuanyuan.me/library/python:3.11-bullseye

ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_VERSION=16

# 使用国内 apt 源加速构建
RUN sed -i 's|http://deb.debian.org|https://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list \
 && sed -i 's|http://security.debian.org|https://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list \
 && apt-get update && apt-get install -y --no-install-recommends \
    git curl ca-certificates gnupg2 build-essential \
    libxml2-dev libxslt1-dev libldap2-dev libsasl2-dev libffi-dev libpq-dev \
    libjpeg-dev zlib1g-dev liblcms2-dev libblas-dev libatlas-base-dev libpng-dev \
    fontconfig xfonts-75dpi xfonts-base xfonts-utils \
 && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 16 和 Less 插件
RUN curl -fsSL https://deb.nodesource.com/setup_16.x | bash - \
 && apt-get update && apt-get install -y nodejs \
 && npm install -g less less-plugin-clean-css \
 && rm -rf /var/lib/apt/lists/*

# 复制并安装 wkhtmltopdf 离线包
COPY deps/wkhtmltox_0.12.6-1.buster_amd64.deb /tmp/
RUN apt-get update && apt-get install -y /tmp/wkhtmltox_0.12.6-1.buster_amd64.deb \
 && rm /tmp/wkhtmltox_0.12.6-1.buster_amd64.deb \
 && rm -rf /var/lib/apt/lists/*

# 拷贝代码，设置工作目录
WORKDIR /opt/odoo
COPY . /opt/odoo

# 安装 Python 依赖
RUN pip install --upgrade pip \
 && pip install -r /opt/odoo/odoo-server/requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 开放 Odoo 默认端口
EXPOSE 8069

# 启动命令
CMD ["python3", "/opt/odoo/odoo-server/odoo-bin", "-c", "/opt/odoo/etc/odoo.conf"]

```

6.5、构建镜像

```
cd /opt/odoo
docker build -t my-odoo:18.0 .
```

6.6、创建docker网络，确保odoo与数据库连接

```
docker network create odoo-net
docker network connect odoo-net odoo-db
```

6.7、启动odoo容器并连接数据库

```
docker run -d \
  --name odoo-server \
  --network odoo-net \
  --restart unless-stopped \
  -e TZ=Asia/Shanghai \
  -p 8069:8069 \
  -v /opt/odoo:/opt/odoo \
  registry.cn-shanghai.aliyuncs.com/sucloud/my-odoo:18.0 \
  python3 /opt/odoo/odoo-server/odoo-bin -c /opt/odoo/etc/odoo.conf
```

6.9、浏览器验证

```
http://ip:8069
```

七、资源限制（可选）

```
docker update odoo-server --cpus=1 --memory=512m--memory-swap=512m
docker update odoo-db --cpus=1 --memory=256m --memory-swap=256m
```

先进行观察

```
docker stats   #然后分配资源限制
```

八、后续可选，nginx代理+https协议

方法一、容器部署nginx

```
docker run -d \
>   --name nginx-odoo \
>   --network odoo-net \
>   -p 80:80 -p 443:443 \
>   -v /opt/odoo/odoo-nginx/odoo.conf:/etc/nginx/conf.d/odoo.conf:ro \ 
>   -v /etc/ssl/certs:/etc/ssl/certs:ro \
>   -v /etc/ssl/private:/etc/ssl/private:ro \
>   nginx:1.18 
```

示例配置文件

```
server {
    listen 80;
    server_name erp.odoo.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name erp.odoo.com;

    ssl_certificate /etc/nginx/certs/fullchain.pem;
    ssl_certificate_key /etc/nginx/certs/privkey.pem;

    location / {
        proxy_pass http://odoo-server:8069;  # 用容器名访问
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }
}
```

**PS：需提前创建证书**

内网环境创建证书

```
Generating RSA private key, 2048 bit long modulus
# 接下来,使用私钥创建一个新的证书签名请求（CSR）。这个CSR内包含了公钥以及其他一些附加信息。
openssl genrsa -out server.key 2048
openssl req -new -key server.key -out server.csr
openssl req -new -x509 -days 3650 -key server.key -out server.crt -subj "/C=CH/ST=mykey/L=mykey/O=mykey/OU=mykey/CN=www.test.com/CN=www.test1.com/CN=www.test2.com"
 
# req  --> 用于创建新的证书请求
# new  --> 表示创建的是新证书    
# x509 --> 表示定义证书的格式为标准格式
# days --> 表示证书的有效期
# key  --> 表示调用的私钥文件信息
# out  --> 表示输出证书文件信息
```

将创建好的证书放入目录下

```
server.key server.csr server.crt
 ssl_certificate /etc/nginx/ssl_key/server.crt;
 ssl_certificate_key /etc/nginx/ssl_key/server.key;

```

方法二：

```
#安装 certbot 和 Nginx 插件：
dnf install -y certbot python3-certbot-nginx
#签发证书并自动配置nginx
certbot --nginx -d test.com
```

