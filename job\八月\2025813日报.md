### 2025/8/13日报

**今日工作内容**：

1. **开源固定资产管理系统调研与测试**
   - 寻找并对比了三个开源固定资产管理系统：**WGFIX**、**Snipe-IT** 和 **EAMApp**。
   - 分别部署了测试版本，评估系统功能、UI美观性以及部署难易度。
   - 最终选定 **EAMApp** 作为后续使用系统，计划采用 **Docker Compose** 方式部署正式版本，当前仍需进一步功能完善与优化。
2. **鸿之微服务器节点登录问题排查**
   - 发现鸿之微服务器部分节点无法正常登录，仅能使用最小化 Shell 登录。
   - 怀疑问题与 `.bashrc` / `.profile` 或 `/etc/profile` 文件中存在阻塞命令有关。
   - 排查过程中出现登录节点也无法再登录的情况，最终通过客服联系分配了新的节点。
   - 在新节点上重新提交作业后可正常登录。
3. **学校软路由下载任务与存储状态检查**
   - 准备清理学校软路由上的下载任务时，发现挂载的存储已掉线。
   - 当前无下载任务，暂不进行处理。
   - 后续由王老师现场检查存储问题。