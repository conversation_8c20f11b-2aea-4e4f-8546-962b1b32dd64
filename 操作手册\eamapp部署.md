### eamapp部署

一、上传压缩包

```
scp eampapp.tar.gz root@ip:/opt
```

二、解压

```
tar -zxvf eampapp.tar.gz
```

三、进入dockercompose目录

```
cd eamapp/eam/dk_project/dk_app/foxnic/foxnic_jK35
ls
[root@bh004 foxnic_jK35]# ls
app  docker-compose.yml
```

四、修改docker-compose.yml

```yaml
#完整的yml文件，包含主app，bpm，mysql，redis，minio，nginx。
#docker-compose.yml
services:
  app_mysql:
    image: registry.cn-hangzhou.aliyuncs.com/lank/mysql:fapp_db_${VERSION}
    restart: always
    volumes:
      - ${APP_PATH}/app/docker/mysql/log:/var/log
      - ${APP_PATH}/app/docker/mysql/data:/var/lib/mysql
      - ${APP_PATH}/app/docker/mysql/conf.d:/etc/mysql/conf.d
      - /etc/localtime:/etc/localtime:ro
    command:
      --max_connections=4096
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --lower_case_table_names=1
      --max_allowed_packet=100M
      --server-id=1
      --log-bin=mysql-bin
      --binlog_expire_logs_seconds=86400
      --binlog-format=ROW
      --binlog_cache_size=256m
      --slow_query_log=1
      --long_query_time=2
      --slow_query_log_file=/var/lib/mysql/dev_app_mysql-slow.log
      --innodb_buffer_pool_size=1G
    environment:
      APP_MODE: '["eam_sq"]'
      MYSQL_ROOT_HOST: '%'
      MYSQL_ROOT_PASSWORD: 'P@ssw0rd123456'
      MYSQL_DATABASE: 'app'
      MYSQL_USER: 'app'
      MYSQL_PASSWORD: 'P@ssw0rd123456'
      TZ: 'Asia/Shanghai'
    privileged: true
    networks:
      foxnic_jK35_network:
        ipv4_address: ***********
    labels:
      createdBy: "bt_apps"    
      
  app_oss:
    image: registry.cn-hangzhou.aliyuncs.com/lank/minio:20240803
    restart: always
    ports:
      - "39000:9000"
      - "39090:9090"
    volumes:
      - ${APP_PATH}/app/docker/oss/data:/data
      - ${APP_PATH}/app/docker/oss/config:/root/.minio
    command: server /data --console-address ":9000" --address ":9090"
    environment:
      MINIO_ACCESS_KEY: 'admin'
      MINIO_SECRET_KEY: 'P@ssw0rd123456'
      MINIO_BROWSER: 'on'
    networks:
      foxnic_jK35_network:
        ipv4_address: ***********
    labels:
      createdBy: "bt_apps"    
    
  app_app:
    image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_app_${VERSION}
    restart: always
    ports:
      - "8089:8089"
    working_dir: /home
    volumes:
      - ${APP_PATH}/app/docker/app/home:/home
      - ${APP_PATH}/app/docker/app/netdisk:/app/app/app/netdisk
      - ${APP_PATH}/app/docker/app/upload:/app/app/app/upload
    networks:
      foxnic_jK35_network:
        ipv4_address: ***********
    environment:
      REDIS_HOST_IP: ***********
      BPM_HOST_IP: ***********
      MYSQL_HOST_IP: ***********
      MYSQL_HOST_PORT: 3306
      MYSQL_DATABASE: 'app'
      MYSQL_USER: 'app'
      MYSQL_PASSWORD: 'P@ssw0rd123456'
      STORAGE_TYPE: 'minio'
      MINIO_URL: 'http://***********:9090'
      MINIO_ACCESS_KEY: 'admin'
      MINIO_SECRET_KEY: 'P@ssw0rd123456'
      MINIO_ACCESS_KEY_YML: 'iLD9PcLSqv5Q0ELXpXQN'
      MINIO_SECRET_KEY_YML: 'nTdOWm5rW54ZpyZ6iCJovEedOwUq5uXe9W6H4kHW'
      TZ: 'Asia/Shanghai'
    depends_on:
      - app_mysql
      - app_oss
    labels:
      createdBy: "bt_apps"
      
  app_bpm:
    image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_bpm_${VERSION}
    restart: always
    ports:
      - "8099:8099"
    working_dir: /home
    user: root
    privileged: true
    volumes:
      - ${APP_PATH}/app/docker/bpm/home:/home
    networks:
      foxnic_jK35_network:
        ipv4_address: ***********
    environment:
      APP_HOST_IP: ***********
      MYSQL_HOST_IP: ***********
      MYSQL_HOST_PORT: 3306
      MYSQL_DATABASE: 'app'
      MYSQL_USER: 'app'
      MYSQL_PASSWORD: 'P@ssw0rd123456'
      TZ: 'Asia/Shanghai'
    depends_on:
      - app_mysql
    labels:
      createdBy: "bt_apps"
      
  foxnic_jK35:
    image: registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_nginx_${VERSION}
    restart: always
    ports:
      - ${HOST_IP}:${WEB_HTTP_PORT1}:8088
      - ${HOST_IP}:${WEB_HTTP_PORT2}:8091
    volumes:
      - ${APP_PATH}/app/docker/nginx/log:/var/log/nginx
    user: root
    privileged: true
    networks:
      foxnic_jK35_network:
        ipv4_address: ***********
    labels:
      createdBy: "bt_apps"

networks:
  foxnic_jK35_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24
```

```yaml
1. app_mysql（数据库服务）
image: 使用了私有仓库的 MySQL 镜像，版本通过 ${VERSION} 变量控制。
volumes:
${APP_PATH}/app/docker/mysql/log:/var/log 日志持久化
${APP_PATH}/app/docker/mysql/data:/var/lib/mysql 数据持久化
${APP_PATH}/app/docker/mysql/conf.d:/etc/mysql/conf.d 配置文件挂载
/etc/localtime:/etc/localtime:ro 保证容器时间与宿主机一致
command: 启动参数，设置连接数、字符集、日志等
environment: 数据库账号、密码、数据库名、时区等
networks: 指定自定义网络和固定IP
privileged: true，容器有更高权限
labels: 标记信息
2. app_oss（对象存储服务，MinIO）
image: MinIO 镜像
ports:
39000:9000（控制台）
39090:9090（API）
volumes:
${APP_PATH}/app/docker/oss/data:/data 数据持久化
${APP_PATH}/app/docker/oss/config:/root/.minio 配置持久化
command: 启动参数
environment: 账号、密码等
networks: 指定自定义网络和固定IP
3. app_app（主应用服务）
image: 主应用镜像
ports: 8089:8089
working_dir: /home
volumes:
${APP_PATH}/app/docker/app/home:/home 主目录
${APP_PATH}/app/docker/app/netdisk:/app/app/app/netdisk 网盘目录
${APP_PATH}/app/docker/app/upload:/app/app/app/upload 上传目录
environment: 依赖的服务IP、数据库信息、MinIO信息等
depends_on: 依赖数据库和OSS服务
networks: 指定自定义网络和固定IP
4. app_bpm（流程服务）
image: BPM镜像
ports: 8099:8099
working_dir: /home
user: root
privileged: true
volumes:
${APP_PATH}/app/docker/bpm/home:/home
environment: 依赖服务IP、数据库信息等
depends_on: 依赖数据库服务
networks: 指定自定义网络和固定IP
5. foxnic_jK35（Nginx服务）
image: Nginx镜像
ports:
${HOST_IP}:${WEB_HTTP_PORT1}:8088
${HOST_IP}:${WEB_HTTP_PORT2}:8091
volumes:
${APP_PATH}/app/docker/nginx/log:/var/log/nginx 日志挂载
user: root
privileged: true
networks: 指定自定义网络和固定IP
6. networks（自定义网络）
foxnic_jK35_network:
driver: bridge
ipam 指定了子网 **********/24，每个服务分配了固定IP，便于服务间通信。
```

**目录结构**

```
${APP_PATH}/app/docker/
├── mysql/                          # MySQL数据库
│   ├── log/                        # MySQL日志文件
│   ├── data/                       # MySQL数据文件
│   └── conf.d/                     # MySQL配置文件
│
├── oss/                            # MinIO对象存储
│   ├── data/                       # 对象存储数据
│   └── config/                     # MinIO配置文件
│
├── app/                            # 主应用
│   ├── home/                       # 应用主目录
│   ├── netdisk/                    # 网盘数据
│   └── upload/                     # 上传文件
│
├── bpm/                            # 工作流引擎
│   └── home/                       # BPM主目录
│
└── nginx/                          # Nginx反向代理
    └── log/                        # Nginx日志文件
```

**按需修改**

启动

```
docker compose up -d     #后台启动
```

检查

```
docker ps -a
```

测试

```
http://ip:8089
```

**docker-compose文件详解**

```
1. 整体架构概览
这个系统包含5个主要服务：
MySQL数据库 (app_mysql)
对象存储 (app_oss - MinIO)
主应用 (app_app)
工作流引擎 (app_bpm)
反向代理 (foxnic_jK35 - Nginx)
```

**mysql数据库服务**

```yaml
app_mysql:
  image: registry.cn-hangzhou.aliyuncs.com/lank/mysql:fapp_db_${VERSION}
  restart: always
  volumes:
    - ${APP_PATH}/app/docker/mysql/log:/var/log
    - ${APP_PATH}/app/docker/mysql/data:/var/lib/mysql
    - ${APP_PATH}/app/docker/mysql/conf.d:/etc/mysql/conf.d
    - /etc/localtime:/etc/localtime:ro
```

- 数据持久化：mysql/data 目录包含所有数据库文件

- 日志管理：mysql/log 目录（虽然当前为空）

- 配置扩展：mysql/conf.d 目录用于添加自定义配置

- 时区同步：/etc/localtime 确保容器时间与宿主机一致

关键配置参数：

- 最大连接数：4096

- 字符集：utf8mb4

- 二进制日志：启用，保留1天

- 慢查询日志：启用，阈值2秒

- InnoDB缓冲池：1GB

**对象存储服务 (app_oss - MinIO)**

```yaml
app_oss:
  image: registry.cn-hangzhou.aliyuncs.com/lank/minio:20240803
  ports:
    - "39000:9000"    # API端口
    - "39090:9090"    # 管理控制台端口
  volumes:
    - ${APP_PATH}/app/docker/oss/data:/data
    - ${APP_PATH}/app/docker/oss/config:/root/.minio
```

- 数据存储：oss/data 目录包含所有上传的文件

- 配置管理：oss/config 目录包含MinIO配置

- 端口管理：39000(API)、39090(管理界面)

- 访问密钥：admin/P@ssw0rd123456

**主应用服务 (app_app)**

```yaml
app_app:
  image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_app_${VERSION}
  ports:
    - "8089:8089"
  volumes:
    - ${APP_PATH}/app/docker/app/home:/home
    - ${APP_PATH}/app/docker/app/netdisk:/app/app/app/netdisk
    - ${APP_PATH}/app/docker/app/upload:/app/app/app/upload
```

- 应用主目录：app/home 包含应用配置和运行时数据

- 网盘数据：app/netdisk 包含用户网盘文件

- 上传文件：app/upload 包含用户上传的各种文件

- 应用端口：8089

**工作流引擎服务 (app_bpm)**

```
app_bpm:
  image: registry.cn-hangzhou.aliyuncs.com/lank/app:fapp_bpm_${VERSION}
  ports:
    - "8099:8099"
  volumes:
    - ${APP_PATH}/app/docker/bpm/home:/home
```

- BPM数据：bpm/home 包含工作流定义和实例数据

- 工作流端口：8099

**反向代理服务**

```yaml
foxnic_jK35:
  image: registry.cn-hangzhou.aliyuncs.com/lank/nginx:fapp_nginx_${VERSION}
  ports:
    - ${HOST_IP}:${WEB_HTTP_PORT1}:8088
    - ${HOST_IP}:${WEB_HTTP_PORT2}:8091
  volumes:
    - ${APP_PATH}/app/docker/nginx/log:/var/log/nginx
```

- 访问日志：nginx/log 包含访问和错误日志

- 端口映射：通过环境变量配置HTTP端口

- 负载均衡：可能配置多个端口用于不同服务

**网络配置**

```yaml
networks:
  foxnic_jK35_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/24
```

- 固定IP分配：每个服务都有固定的内网IP

- 网络隔离：所有服务在同一网络中通信

- IP规划：确保IP地址不冲突

**环境变量管理**

关键环境变量：

- ${VERSION}：镜像版本

- ${APP_PATH}：应用根路径

- ${HOST_IP}：宿主机IP

- ${WEB_HTTP_PORT1}：HTTP端口1

- ${WEB_HTTP_PORT2}：HTTP端口2

**数据备份**

- 数据库备份：备份mysql/data目录

- 文件备份：备份oss/data、app/upload等目录

- 配置备份：备份整个docker目录



**备份脚本**

```
#!/bin/bash

# 配置参数
BACKUP_DIR="/opt/eamapp/db-backup/"  
CONTAINER_NAME="app_mysql"       
DB_USER="app"                   
DB_PASSWORD="P@ssw0rd123456"  
DATE=$(date +"%Y%m%d%H%M%S")    
RETENTION_DAYS=30           
BACKUP_FILE="${BACKUP_DIR}/mysql_backup_all_${DATE}.sql"
BACKUP_FILE_COMPRESSED="${BACKUP_FILE}.gz" 


export MYSQL_PWD=$DB_PASSWORD


mkdir -p ${BACKUP_DIR}


docker exec ${CONTAINER_NAME} mysqldump -u ${DB_USER} -p${DB_PASSWORD} --all-databases --single-transaction --routines --triggers --events > ${BACKUP_FILE}


gzip ${BACKUP_FILE}


find ${BACKUP_DIR} -type f -name "mysql_backup_all_*.sql.gz" -mtime +${RETENTION_DAYS} -exec rm -f {} \;
find ${LOG_DIR} -type f -name "*.log" -mtime +${RETENTION_DAYS} -exec rm -f {} \;

echo "MySQL all-databases backup completed: ${BACKUP_FILE_COMPRESSED}"
```

**计划任务**

```
30 2 * * * /opt/eamapp/backup/eam-db-backup.sh  >> /opt/eamapp/db-backup/backup.log 2>&1
```















构建jar包

```
cd /opt/eam-2.9.1/eam-2.9.1/platform && \
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.462.b08-3.el9.x86_64 && \
export PATH=$JAVA_HOME/bin:$PATH && \
export MAVEN_OPTS="-Xmx2g" && \
mvn dependency:resolve -U -Dmaven.repo.local=~/.m2/repository/
```

```
mvn clean package -Dmaven.repo.local=~/.m2/repository/
mvn clean install -Dmaven.repo.local=~/.m2/repository/
```

**生成两个jar包一个app的一个bpm的**

```
[root@bh004 platform]# ls wrapper/wrapper-all/target/
classes  generated-sources  lib  maven-archiver  maven-status  wrapper-all-*******.jar  wrapper-all-*******.jar.original
```

```
[root@bh004 platform]# ls wrapper/wrapper-camunda/target/
classes  generated-sources  lib  maven-archiver  maven-status  wrapper-camunda-*******.jar  wrapper-camunda-*******.jar.original
```

