# 2025年8月工作月报

## 一、月度工作概览

本月主要围绕系统迁移、容器化部署、文档编写、数据管理等方面开展工作，涉及多个技术平台和业务系统的运维与优化。

## 二、主要工作内容（按时间顺序）

### 8月1日
- 进行Kubernetes迁移测试，完成服务迁移可行性验证，编写部署所需YAML文件
- 整理种子文件下载进度并统计
- 安排DOI文件下载任务，利用校园网环境进行批量下载
- 了解服务器部署情况，因服务器尚未正式上架，仅做前期准备工作

### 8月4日
- 完成已下载DOI文件的整理并交付给潘老师
- 统计种子下载进度情况
- 测试Nginx容器代理功能，成功配置HTTPS协议
- 去除Odoo系统中任务进度相关的百分比字段，简化界面显示
- 配合处理文物遗址远程监控平台异常
- 评估ThinkPad与华为擎云两款笔记本的适用性

### 8月5日
- 重新安装两台新采购笔记本的Windows系统，完成全部驱动程序安装
- 模拟Odoo及相关数据库的迁移过程，验证迁移流程可行性
- 测试MySQL数据库迁移后的账户权限问题，解决权限失效问题
- 完成新打印机初始化配置及基础功能测试

### 8月6日
- 研究容器中使用GPU的技术方案，包括NVIDIA Container Toolkit配置
- 测试容器内GPU负载情况，验证容器内任务对GPU调用无性能损耗
- 探索容器内多GPU分配方案，为后续多任务调度做技术准备
- 整理从裸机到容器使用GPU的全流程文档

### 8月7日
- 完成与hzw的种子文献下载进度统计工作
- 继续撰写容器技术相关文档，涵盖概念、操作流程及问题排查
- 排查数据库连接问题（cmd命令行正常，navicat等工具无法连接）

### 8月8日
- 完成《容器 vs 虚拟机》对比分析文档
- 编写容器核心技术原理，包括Docker与容器Runtime
- 整理容器内CPU与GPU使用方式与调度机制说明
- 描述容器进程模型及CPU实际使用率计算方法
- 整理OOM机制及常见排查思路
- 新建MySQL账号供数据工程师使用，并授予必要权限

### 8月11日
- 停止学校种子下载任务（速度过慢），重新分配至本地三台软路由
- 实现下载任务均衡分配，提高整体下载效率
- 清理其他来源的重复下载任务
- 制作统一表格并使用颜色标记下载进度和状态
- 继续编写容器技术文档，新增容器进程管理、OOM机制、磁盘配额等内容

### 8月12日
- 持续更新种子下载进度表格，标注任务下载状态
- 继续编写容器技术系统文档
- 排查鸿之微服务器登录问题
- 调研并部署两款开源资产管理系统，采用Docker容器方式部署

### 8月13日
- 对比调研三个开源固定资产管理系统：WGFIX、Snipe-IT和EAMApp
- 选定EAMApp并计划采用Docker Compose方式部署
- 排查鸿之微服务器节点登录问题，通过客服获得新节点
- 发现学校软路由存储掉线问题，待王老师现场检查

### 8月14日
- 完善EAM系统的docker-compose配置文件
- 编写Odoo与PostgreSQL、MySQL数据库的docker-compose配置
- 测试Nginx容器代理服务功能
- 编写HTTP和HTTPS协议两种模板配置文件
- 持续整理种子下载进度表格

### 8月18日
- 持续整理种子下载进度表格
- 将通过其他方式下载的种子上传至存储统一管理
- 继续完善EAM系统的docker-compose文件（Redis、MySQL、MinIO、Nginx、BPM、App等）
- 解决部署过程中的端口冲突问题
- 熟悉EAM系统操作与核心功能

### 8月19日
- 持续整理种子下载进度
- 完善容器技术相关文档
- 进一步整理Docker-Compose配置文件
- 解决端口冲突问题，实现服务正常运行
- 处理鸿之微服务器卡顿问题，联系客服处理性能瓶颈
- 配合文物遗址监控的临时停止工作

### 8月20日
- 继续完善EAM系统docker-compose配置及容器技术文档
- 整理服务端口映射
- 持续整理种子下载进度和已有下载数据
- 深入了解EAM系统操作流程及核心功能
- 检查各项服务运行状态，确认环境无异常
- 反馈东方超算的显卡掉线问题

### 8月21日
- 持续整理种子下载进度，上传文献文件至软路由存储
- 将EAM系统镜像上传至私有仓库，方便后续部署与版本管理
- 移除Redis模块，优化EAM系统架构并整理目录结构
- 对docker-compose架构进行梳理，包含MySQL、MinIO、主应用App、BPM应用App、Nginx等5个服务
- 协助其他工程师解决端口转发问题

### 8月22日
- 修复Odoo的`hongzhiwei_project`模块中装饰器错误问题（@api.model改为@api.model_create_multi）
- 对EAM系统涉及的所有服务进行配置整理
- 整理Odoo与MySQL在Docker环境下的备份方案，编写备份脚本
- 登录东方超算平台进行分配和维护

### 8月25日
- 持续跟踪和整理种子下载进度
- 根据需求启动并维护东方超算的容器实例
- 完成从其他校区到本地的跨区域数据拷贝任务
- 继续优化EAM系统配置文件，完善容器技术文档

### 8月26日
- 将数据进行二次备份，拷贝至第二份移动硬盘
- 将备份数据闪送至鸿之微
- 排查超算服务器故障问题并实施解决措施
- 处理鸿之微服务器断开连接问题，恢复服务稳定性
- 配合重新启动文物遗址监测系统

### 8月27日
- 反馈超算服务器数据盘与标识不一致问题
- 配合完成文物遗址监测系统自检1-90的电阻测试
- 排查并解决鸿之微服务器计算节点无法SSH登录问题
- 进一步完善EAM系统的docker-compose文件
- 尝试进行EAM系统JAR包的本地构建，处理私有仓库依赖问题

### 8月28日
- 处理构建Jar包时的私库访问问题
- 将学校工作站迁移到公司并重装为Linux系统
- 成功迁移并部署Odoo、PostgreSQL、MySQL、EAM系统
- 配置Nginx代理及端口转发，确保所有服务可通过公网访问
- 重新创建超算实例并完成数据盘扩容，迁移原有数据

### 8月29日
- 解决迁移后Odoo与EAM系统的报错问题
- 为Odoo的PostgreSQL数据库和EAM的MySQL数据库编写备份脚本
- 配置数据库定期自动备份计划任务
- 观察所有容器的资源使用情况（CPU、内存、网络、磁盘）
- 整理当前种子下载进度并进行汇报
- 对系统日志进行检查，确保无重大异常

## 三、技术成长与收获

### 容器技术深化
- 深入理解Docker与Kubernetes技术架构
- 掌握GPU容器化部署与性能优化
- 熟练运用docker-compose进行多服务编排
- 积累容器安全与治理经验

### 系统运维能力提升
- 提升Linux系统管理与服务部署能力
- 加强数据库备份与恢复方案设计
- 熟练掌握Nginx代理配置与端口管理
- 增强跨平台系统迁移经验

### 问题解决能力
- 提升复杂技术问题诊断与解决能力
- 加强多系统集成与兼容性处理经验
- 培养预防性维护与监控意识

## 四、存在问题与改进

### 当前问题
1. 部分服务器连接稳定性需要持续关注
2. 容器资源使用情况需要进一步优化
3. 系统监控与告警机制有待完善

### 改进计划
1. 建立更完善的系统监控体系
2. 优化容器资源配置与性能调优
3. 完善自动化运维脚本和流程
4. 加强预防性维护措施

## 五、下月工作计划

1. **系统优化**：继续完善容器化部署架构，优化资源配置
2. **文档完善**：补充实践案例与性能测试结果到技术文档
3. **备份完善**：完善EAM系统数据备份方案
4. **监控建设**：建立系统性能监控与告警机制
5. **技术研究**：深入研究容器编排与微服务架构

---

**报告期间**：2025年8月1日 - 2025年8月29日  
**报告人**：[您的姓名]  
**报告日期**：2025年8月30日