### 2025/7/31日报

### 一、Odoo 系统介绍与演示

今日对三位行政同事进行了 Odoo 系统的基础使用培训，内容包括：

- Odoo 模块化架构介绍（如项目管理、人事、审批等模块的作用）
- 创建用户及用户管理流程演示
- 权限组与角色配置简要说明，帮助其理解角色间权限隔离
- 强调当前系统为测试用途，数据不保留，后台数据库已提前完成备份，仅保留导入的项目模板数据供熟悉使用流程

目的为提前熟悉后续将上线的新系统，降低未来迁移上线时的适应成本。

------

### 二、MySQL 容器环境下备份脚本优化

- 将原有物理机环境下的 MySQL 自动备份脚本适配为容器环境使用
- 使用 `docker exec` 结合定时任务实现自动备份指定数据库
- 已在当前容器环境下完成脚本测试，验证备份文件正确生成并可恢复

------

### 三、监控系统准备

- 已完成监控系统相关的 `docker-compose.yml` 编写
- 包括 Prometheus、Grafana、Node Exporter、cAdvisor 等服务
- 等待后续服务迁移完毕后同步部署并统一纳入监控体系

------

### 四、后续计划

- 持续推进 Odoo、MySQL、PostgreSQL 服务容器化迁移与测试
- 配合行政使用反馈调整权限和操作逻辑，提前为上线做好准备